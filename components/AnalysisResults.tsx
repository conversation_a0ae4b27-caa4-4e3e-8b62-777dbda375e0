import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Sparkles, TrendingUp, Clock, Star, ChevronDown, ChevronUp } from 'lucide-react-native';
import { AIAnalysisResult, ConditionTag } from '@/services/gemini';
import { analytics } from '@/services/analytics';

interface AnalysisResultsProps {
  result: AIAnalysisResult;
  onProductRecommendations: () => void;
  onFeedback: (rating: number, feedback?: string) => void;
}

export default function AnalysisResults({ result, onProductRecommendations, onFeedback }: AnalysisResultsProps) {
  const [expandedInsights, setExpandedInsights] = useState(false);
  const [expandedRoutine, setExpandedRoutine] = useState(false);
  const [userRating, setUserRating] = useState<number | null>(null);

  const handleRating = (rating: number) => {
    setUserRating(rating);
    onFeedback(rating);
    analytics.trackUserFeedback(result.analysisId, rating);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return '#10B981';
    if (confidence >= 60) return '#F59E0B';
    return '#EF4444';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'mild': return '#10B981';
      case 'moderate': return '#F59E0B';
      case 'severe': return '#EF4444';
      default: return '#6B7280';
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Confidence Header */}
      <View style={styles.confidenceCard}>
        <LinearGradient
          colors={['#8B5CF6', '#7C3AED']}
          style={styles.confidenceGradient}
        >
          <Sparkles size={32} color="#FFFFFF" />
          <Text style={styles.confidenceTitle}>Analysis Complete</Text>
          <Text style={styles.confidenceText}>
            {result.overallConfidence}% Confidence
          </Text>
          <Text style={styles.analysisType}>
            {result.inputType === 'hybrid' ? 'Photo + Questionnaire Analysis' : 'Questionnaire Analysis'}
          </Text>
        </LinearGradient>
      </View>

      {/* Primary Condition */}
      <View style={styles.primaryConditionCard}>
        <Text style={styles.sectionTitle}>Primary Condition</Text>
        <Text style={styles.primaryConditionText}>{result.primaryCondition}</Text>
        
        {/* Condition Tags */}
        <View style={styles.tagsContainer}>
          {result.conditionTags.slice(0, 3).map((tag, index) => (
            <ConditionTagComponent key={tag.id} tag={tag} />
          ))}
        </View>
      </View>

      {/* Personalized Insights */}
      <View style={styles.insightsCard}>
        <TouchableOpacity 
          style={styles.sectionHeader}
          onPress={() => setExpandedInsights(!expandedInsights)}
        >
          <Text style={styles.sectionTitle}>Personalized Insights</Text>
          {expandedInsights ? 
            <ChevronUp size={20} color="#6B7280" /> : 
            <ChevronDown size={20} color="#6B7280" />
          }
        </TouchableOpacity>
        
        {expandedInsights && (
          <View style={styles.insightsContent}>
            {result.personalizedInsights.map((insight, index) => (
              <View key={index} style={styles.insightItem}>
                <View style={styles.insightBullet} />
                <Text style={styles.insightText}>{insight}</Text>
              </View>
            ))}
          </View>
        )}
      </View>

      {/* Recommended Ingredients */}
      <View style={styles.ingredientsCard}>
        <Text style={styles.sectionTitle}>Recommended Ingredients</Text>
        <View style={styles.ingredientsContainer}>
          {result.recommendedIngredients.map((ingredient, index) => (
            <View key={index} style={styles.ingredientTag}>
              <Text style={styles.ingredientText}>{ingredient}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Routine Preview */}
      <View style={styles.routineCard}>
        <TouchableOpacity 
          style={styles.sectionHeader}
          onPress={() => setExpandedRoutine(!expandedRoutine)}
        >
          <Text style={styles.sectionTitle}>Your Routine</Text>
          {expandedRoutine ? 
            <ChevronUp size={20} color="#6B7280" /> : 
            <ChevronDown size={20} color="#6B7280" />
          }
        </TouchableOpacity>
        
        {expandedRoutine && (
          <View style={styles.routineContent}>
            <View style={styles.routineSection}>
              <Text style={styles.routineTimeTitle}>Morning Routine</Text>
              {result.routineRecommendations.morning.map((step, index) => (
                <View key={index} style={styles.routineStep}>
                  <View style={styles.stepNumber}>
                    <Text style={styles.stepNumberText}>{step.order}</Text>
                  </View>
                  <View style={styles.stepContent}>
                    <Text style={styles.stepAction}>{step.action}</Text>
                    <View style={styles.stepMeta}>
                      <Clock size={12} color="#6B7280" />
                      <Text style={styles.stepDuration}>{step.duration}</Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>

            <View style={styles.routineSection}>
              <Text style={styles.routineTimeTitle}>Evening Routine</Text>
              {result.routineRecommendations.evening.map((step, index) => (
                <View key={index} style={styles.routineStep}>
                  <View style={styles.stepNumber}>
                    <Text style={styles.stepNumberText}>{step.order}</Text>
                  </View>
                  <View style={styles.stepContent}>
                    <Text style={styles.stepAction}>{step.action}</Text>
                    <View style={styles.stepMeta}>
                      <Clock size={12} color="#6B7280" />
                      <Text style={styles.stepDuration}>{step.duration}</Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}
      </View>

      {/* User Feedback */}
      <View style={styles.feedbackCard}>
        <Text style={styles.sectionTitle}>How accurate was this analysis?</Text>
        <View style={styles.ratingContainer}>
          {[1, 2, 3, 4, 5].map(rating => (
            <TouchableOpacity
              key={rating}
              style={[styles.starButton, userRating === rating && styles.starButtonSelected]}
              onPress={() => handleRating(rating)}
            >
              <Star 
                size={24} 
                color={userRating && rating <= userRating ? '#F59E0B' : '#D1D5DB'} 
                fill={userRating && rating <= userRating ? '#F59E0B' : 'transparent'}
              />
            </TouchableOpacity>
          ))}
        </View>
        {userRating && (
          <Text style={styles.feedbackThankYou}>
            Thank you for your feedback! This helps improve our AI.
          </Text>
        )}
      </View>

      {/* Action Button */}
      <TouchableOpacity style={styles.actionButton} onPress={onProductRecommendations}>
        <LinearGradient
          colors={['#14B8A6', '#0D9488']}
          style={styles.actionGradient}
        >
          <TrendingUp size={20} color="#FFFFFF" />
          <Text style={styles.actionText}>View Product Recommendations</Text>
        </LinearGradient>
      </TouchableOpacity>
    </ScrollView>
  );
}

function ConditionTagComponent({ tag }: { tag: ConditionTag }) {
  const confidenceColor = tag.confidence >= 80 ? '#10B981' : tag.confidence >= 60 ? '#F59E0B' : '#EF4444';
  const severityColor = tag.severity === 'mild' ? '#10B981' : tag.severity === 'moderate' ? '#F59E0B' : '#EF4444';

  return (
    <View style={styles.conditionTag}>
      <View style={styles.tagHeader}>
        <Text style={styles.tagCondition}>{tag.condition.replace(/-/g, ' ')}</Text>
        <View style={[styles.confidenceBadge, { backgroundColor: confidenceColor }]}>
          <Text style={styles.confidenceValue}>{tag.confidence}%</Text>
        </View>
      </View>
      <View style={styles.tagMeta}>
        <View style={[styles.severityBadge, { backgroundColor: severityColor }]}>
          <Text style={styles.severityText}>{tag.severity}</Text>
        </View>
        {tag.affectedAreas.length > 0 && (
          <Text style={styles.affectedAreas}>
            Areas: {tag.affectedAreas.join(', ')}
          </Text>
        )}
      </View>
      <Text style={styles.tagDescription}>{tag.description}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  confidenceCard: {
    margin: 24,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#8B5CF6',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
  },
  confidenceGradient: {
    padding: 32,
    alignItems: 'center',
  },
  confidenceTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginTop: 16,
    marginBottom: 8,
  },
  confidenceText: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  analysisType: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#E0E7FF',
  },
  primaryConditionCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 24,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  primaryConditionText: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#14B8A6',
    marginBottom: 16,
    textAlign: 'center',
  },
  tagsContainer: {
    gap: 12,
  },
  conditionTag: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#14B8A6',
  },
  tagHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  tagCondition: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    textTransform: 'capitalize',
    flex: 1,
  },
  confidenceBadge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  confidenceValue: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  tagMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 12,
  },
  severityBadge: {
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  severityText: {
    fontSize: 11,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    textTransform: 'uppercase',
  },
  affectedAreas: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textTransform: 'capitalize',
  },
  tagDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 20,
  },
  insightsCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 24,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  insightsContent: {
    marginTop: 16,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  insightBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#14B8A6',
    marginTop: 6,
    marginRight: 12,
  },
  insightText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 20,
  },
  ingredientsCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 24,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  ingredientsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  ingredientTag: {
    backgroundColor: '#F0FDFA',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#14B8A6',
  },
  ingredientText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#14B8A6',
    textTransform: 'capitalize',
  },
  routineCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 24,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  routineContent: {
    marginTop: 16,
  },
  routineSection: {
    marginBottom: 20,
  },
  routineTimeTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  routineStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#14B8A6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumberText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  stepContent: {
    flex: 1,
  },
  stepAction: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  stepMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  stepDuration: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  feedbackCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 24,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
    marginTop: 12,
  },
  starButton: {
    padding: 8,
  },
  starButtonSelected: {
    backgroundColor: '#FEF3C7',
    borderRadius: 8,
  },
  feedbackThankYou: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#10B981',
    textAlign: 'center',
    marginTop: 12,
  },
  actionButton: {
    marginHorizontal: 24,
    marginBottom: 32,
    borderRadius: 16,
    overflow: 'hidden',
  },
  actionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    gap: 12,
  },
  actionText: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
});