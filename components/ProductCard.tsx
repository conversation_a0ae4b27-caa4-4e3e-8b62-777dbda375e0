import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Star, ShoppingCart } from 'lucide-react-native';
import { Product } from '@/types';

interface ProductCardProps {
  product: Product;
  onPress?: () => void;
  onAddToCart?: () => void;
  isInCart?: boolean;
}

export default function ProductCard({ 
  product, 
  onPress, 
  onAddToCart, 
  isInCart = false 
}: ProductCardProps) {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <Image source={{ uri: product.image }} style={styles.image} />
      
      <View style={styles.content}>
        <Text style={styles.name} numberOfLines={2}>{product.name}</Text>
        
        <View style={styles.ratingContainer}>
          <Star size={14} color="#F59E0B" />
          <Text style={styles.rating}>{product.rating}</Text>
        </View>
        
        <Text style={styles.price}>${product.price.toFixed(2)}</Text>
        
        <View style={styles.benefitsContainer}>
          {product.benefits.slice(0, 2).map((benefit, index) => (
            <View key={index} style={styles.benefitTag}>
              <Text style={styles.benefitText}>{benefit}</Text>
            </View>
          ))}
        </View>

        {onAddToCart && (
          <TouchableOpacity
            style={[styles.cartButton, isInCart && styles.cartButtonActive]}
            onPress={onAddToCart}
          >
            <ShoppingCart size={16} color={isInCart ? '#FFFFFF' : '#14B8A6'} />
            <Text style={[styles.cartButtonText, isInCart && styles.cartButtonTextActive]}>
              {isInCart ? 'Added' : 'Add to Cart'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    marginBottom: 16,
  },
  image: {
    width: '100%',
    height: 150,
  },
  content: {
    padding: 16,
  },
  name: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 8,
    lineHeight: 22,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 8,
  },
  rating: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  price: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#14B8A6',
    marginBottom: 12,
  },
  benefitsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 12,
  },
  benefitTag: {
    backgroundColor: '#F0FDFA',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  benefitText: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#14B8A6',
  },
  cartButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F0FDFA',
    borderRadius: 8,
    paddingVertical: 8,
    gap: 6,
    borderWidth: 1,
    borderColor: '#14B8A6',
  },
  cartButtonActive: {
    backgroundColor: '#14B8A6',
    borderColor: '#0D9488',
  },
  cartButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#14B8A6',
  },
  cartButtonTextActive: {
    color: '#FFFFFF',
  },
});