import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Modal } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Camera, Image as ImageIcon, X, Info } from 'lucide-react-native';
import ImageProcessor, { ImageData } from '@/services/imageProcessor';
import LoadingSpinner from './LoadingSpinner';

interface ImageCaptureProps {
  type: 'skin' | 'hair';
  onImageCaptured: (image: ImageData) => void;
  onCancel: () => void;
  visible: boolean;
}

export default function ImageCapture({ type, onImageCaptured, onCancel, visible }: ImageCaptureProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [showGuidelines, setShowGuidelines] = useState(false);

  const handleTakePhoto = async () => {
    try {
      setIsProcessing(true);
      const image = await ImageProcessor.takePhoto({
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 0.9,
        allowsEditing: true
      });

      if (image) {
        if (image.quality < 0.5) {
          Alert.alert(
            'Image Quality Warning',
            'The image quality is low. This may affect analysis accuracy. Would you like to retake the photo?',
            [
              { text: 'Retake', onPress: handleTakePhoto },
              { text: 'Use Anyway', onPress: () => onImageCaptured(image) }
            ]
          );
        } else {
          onImageCaptured(image);
        }
      }
    } catch (error) {
      Alert.alert('Camera Error', error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSelectFromLibrary = async () => {
    try {
      setIsProcessing(true);
      const image = await ImageProcessor.captureImage({
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 0.9,
        allowsEditing: true
      });

      if (image) {
        if (image.quality < 0.5) {
          Alert.alert(
            'Image Quality Warning',
            'The selected image quality is low. This may affect analysis accuracy.',
            [
              { text: 'Select Different', onPress: handleSelectFromLibrary },
              { text: 'Use Anyway', onPress: () => onImageCaptured(image) }
            ]
          );
        } else {
          onImageCaptured(image);
        }
      }
    } catch (error) {
      Alert.alert('Image Selection Error', error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const guidelines = ImageProcessor.getImageGuidelines(type);

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <LinearGradient
          colors={type === 'skin' ? ['#14B8A6', '#0D9488'] : ['#EC4899', '#BE185D']}
          style={styles.header}
        >
          <TouchableOpacity style={styles.closeButton} onPress={onCancel}>
            <X size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.title}>Add Photo</Text>
          <Text style={styles.subtitle}>
            Optional: Upload a photo for more accurate AI analysis
          </Text>
        </LinearGradient>

        <View style={styles.content}>
          <TouchableOpacity 
            style={styles.guidelinesButton}
            onPress={() => setShowGuidelines(true)}
          >
            <Info size={20} color="#14B8A6" />
            <Text style={styles.guidelinesText}>Photo Guidelines</Text>
          </TouchableOpacity>

          {isProcessing ? (
            <View style={styles.processingContainer}>
              <LoadingSpinner size={60} color="#14B8A6" />
              <Text style={styles.processingText}>Processing image...</Text>
            </View>
          ) : (
            <View style={styles.optionsContainer}>
              <TouchableOpacity style={styles.optionButton} onPress={handleTakePhoto}>
                <LinearGradient
                  colors={['#14B8A6', '#0D9488']}
                  style={styles.optionGradient}
                >
                  <Camera size={32} color="#FFFFFF" />
                  <Text style={styles.optionTitle}>Take Photo</Text>
                  <Text style={styles.optionSubtitle}>Use camera to capture image</Text>
                </LinearGradient>
              </TouchableOpacity>

              <TouchableOpacity style={styles.optionButton} onPress={handleSelectFromLibrary}>
                <LinearGradient
                  colors={['#8B5CF6', '#7C3AED']}
                  style={styles.optionGradient}
                >
                  <ImageIcon size={32} color="#FFFFFF" />
                  <Text style={styles.optionTitle}>Choose from Library</Text>
                  <Text style={styles.optionSubtitle}>Select existing photo</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          )}

          <TouchableOpacity style={styles.skipButton} onPress={onCancel}>
            <Text style={styles.skipText}>Skip Photo Upload</Text>
          </TouchableOpacity>
        </View>

        {/* Guidelines Modal */}
        <Modal visible={showGuidelines} animationType="fade" transparent>
          <View style={styles.modalOverlay}>
            <View style={styles.guidelinesModal}>
              <View style={styles.guidelinesHeader}>
                <Text style={styles.guidelinesTitle}>Photo Guidelines</Text>
                <TouchableOpacity onPress={() => setShowGuidelines(false)}>
                  <X size={24} color="#6B7280" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.guidelinesContent}>
                <Text style={styles.guidelinesSubtitle}>
                  For best analysis results:
                </Text>
                {guidelines.map((guideline, index) => (
                  <View key={index} style={styles.guidelineItem}>
                    <View style={styles.guidelineBullet} />
                    <Text style={styles.guidelineText}>{guideline}</Text>
                  </View>
                ))}
              </View>

              <TouchableOpacity 
                style={styles.guidelinesCloseButton}
                onPress={() => setShowGuidelines(false)}
              >
                <Text style={styles.guidelinesCloseText}>Got it</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 24,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  closeButton: {
    alignSelf: 'flex-end',
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#E0F2FE',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 24,
  },
  guidelinesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F0FDFA',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 32,
    gap: 8,
  },
  guidelinesText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#14B8A6',
  },
  processingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginTop: 16,
  },
  optionsContainer: {
    flex: 1,
    justifyContent: 'center',
    gap: 20,
  },
  optionButton: {
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
  },
  optionGradient: {
    padding: 32,
    alignItems: 'center',
  },
  optionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginTop: 16,
    marginBottom: 8,
  },
  optionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
  },
  skipButton: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  skipText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  guidelinesModal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    maxWidth: 400,
    width: '100%',
  },
  guidelinesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  guidelinesTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  guidelinesContent: {
    marginBottom: 24,
  },
  guidelinesSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#4B5563',
    marginBottom: 16,
  },
  guidelineItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  guidelineBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#14B8A6',
    marginTop: 6,
    marginRight: 12,
  },
  guidelineText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 20,
  },
  guidelinesCloseButton: {
    backgroundColor: '#14B8A6',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  guidelinesCloseText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});