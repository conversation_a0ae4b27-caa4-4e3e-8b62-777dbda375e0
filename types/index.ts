export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  createdAt: Date;
}

export interface AIAnalysisHistory {
  id: string;
  userId: string;
  analysisResult: import('@/services/gemini').AIAnalysisResult;
  userFeedback?: {
    rating: number;
    feedback?: string;
    timestamp: Date;
  };
  createdAt: Date;
}

export interface DiagnosisData {
  type: 'skin' | 'hair';
  answers: Record<string, string>;
  selectedAreas: string[];
  photoUrl?: string;
  timestamp: Date;
}

export interface AnalysisResult {
  id: string;
  userId: string;
  diagnosisData: DiagnosisData;
  condition: string;
  confidence: number;
  severity: string;
  description: string;
  recommendations: ProductRecommendation[];
  createdAt: Date;
}

export interface Product {
  id: number;
  name: string;
  price: number;
  rating: number;
  image: string;
  benefits: string[];
  ingredients: string[];
  routine: string;
  category: 'cleanser' | 'toner' | 'serum' | 'moisturizer' | 'treatment';
  skinType: string[];
  concerns: string[];
}

export interface ProductRecommendation {
  product: Product;
  priority: number;
  reason: string;
  usage: string;
}

export interface RoutineStep {
  id: number;
  productId: number;
  name: string;
  duration: string;
  description: string;
  timeOfDay: 'morning' | 'evening' | 'both';
  order: number;
}

export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  subtotal: number;
  shipping: number;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered';
  createdAt: Date;
}

export interface OrderItem {
  productId: number;
  quantity: number;
  price: number;
}

export interface ProgressEntry {
  id: string;
  userId: string;
  date: Date;
  photoUrl?: string;
  notes?: string;
  rating: number;
  completedSteps: number[];
}