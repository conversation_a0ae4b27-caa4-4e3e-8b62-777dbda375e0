import { useState, useCallback } from 'react';
import { geminiAI, QuestionnaireData, ImageData, AIAnalysisResult } from '@/services/gemini';
import { analytics } from '@/services/analytics';

export interface UseAIAnalysisReturn {
  isAnalyzing: boolean;
  result: AIAnalysisResult | null;
  error: string | null;
  analyzeCondition: (
    type: 'skin' | 'hair',
    questionnaire: QuestionnaireData,
    selectedAreas: string[],
    image?: ImageData
  ) => Promise<void>;
  clearResult: () => void;
  retryAnalysis: () => Promise<void>;
}

export function useAIAnalysis(): UseAIAnalysisReturn {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [result, setResult] = useState<AIAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [lastAnalysisParams, setLastAnalysisParams] = useState<{
    type: 'skin' | 'hair';
    questionnaire: QuestionnaireData;
    selectedAreas: string[];
    image?: ImageData;
  } | null>(null);

  const analyzeCondition = useCallback(async (
    type: 'skin' | 'hair',
    questionnaire: QuestionnaireData,
    selectedAreas: string[],
    image?: ImageData
  ) => {
    setIsAnalyzing(true);
    setError(null);
    setResult(null);

    // Store params for retry functionality
    setLastAnalysisParams({ type, questionnaire, selectedAreas, image });

    const startTime = Date.now();

    try {
      // Track analysis start
      analytics.trackEvent('ai_analysis_started', {
        type,
        hasImage: !!image,
        selectedAreasCount: selectedAreas.length,
        questionnaireFields: Object.keys(questionnaire).length
      });

      // Perform AI analysis
      const analysisResult = await geminiAI.analyzeCondition(
        type,
        questionnaire,
        selectedAreas,
        image
      );

      const responseTime = Date.now() - startTime;

      // Track performance metrics
      analytics.trackAIPerformance({
        analysisId: analysisResult.analysisId,
        responseTime,
        confidence: analysisResult.overallConfidence,
        conditionsDetected: analysisResult.conditionTags.length
      });

      // Track successful completion
      analytics.trackEvent('ai_analysis_completed', {
        analysisId: analysisResult.analysisId,
        type,
        confidence: analysisResult.overallConfidence,
        conditionsFound: analysisResult.conditionTags.length,
        responseTime
      });

      setResult(analysisResult);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Analysis failed';
      setError(errorMessage);

      // Track error
      analytics.trackEvent('ai_analysis_error', {
        type,
        error: errorMessage,
        responseTime: Date.now() - startTime
      });

      console.error('AI Analysis failed:', err);
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  const retryAnalysis = useCallback(async () => {
    if (!lastAnalysisParams) {
      setError('No previous analysis to retry');
      return;
    }

    const { type, questionnaire, selectedAreas, image } = lastAnalysisParams;
    await analyzeCondition(type, questionnaire, selectedAreas, image);
  }, [lastAnalysisParams, analyzeCondition]);

  const clearResult = useCallback(() => {
    setResult(null);
    setError(null);
    setLastAnalysisParams(null);
  }, []);

  return {
    isAnalyzing,
    result,
    error,
    analyzeCondition,
    clearResult,
    retryAnalysis
  };
}

export default useAIAnalysis;