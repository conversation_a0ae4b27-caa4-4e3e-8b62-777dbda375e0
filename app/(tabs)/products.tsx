import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Search, Filter, Star, Grid2x2 as Grid, List } from 'lucide-react-native';
import ProductCard from '@/components/ProductCard';
import { Product } from '@/types';

export default function ProductsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSkinType, setSelectedSkinType] = useState<string>('all');
  const [priceRange, setPriceRange] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  const categories = [
    { id: 'all', name: 'All Products' },
    { id: 'cleanser', name: 'Cleansers' },
    { id: 'toner', name: 'Toners' },
    { id: 'serum', name: 'Serums' },
    { id: 'moisturizer', name: 'Moisturizers' },
    { id: 'treatment', name: 'Treatments' },
  ];

  const skinTypes = [
    { id: 'all', name: 'All Skin Types' },
    { id: 'oily', name: 'Oily' },
    { id: 'dry', name: 'Dry' },
    { id: 'combination', name: 'Combination' },
    { id: 'sensitive', name: 'Sensitive' },
  ];

  const priceRanges = [
    { id: 'all', name: 'All Prices' },
    { id: 'under-25', name: 'Under $25' },
    { id: '25-50', name: '$25 - $50' },
    { id: 'over-50', name: 'Over $50' },
  ];

  // Mock products data
  const allProducts: Product[] = [
    {
      id: 1,
      name: 'Gentle Argan Cleanser',
      price: 24.99,
      rating: 4.8,
      image: 'https://images.pexels.com/photos/4465831/pexels-photo-4465831.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
      benefits: ['Deep cleansing', 'Hydrates skin', 'Anti-inflammatory'],
      ingredients: ['Argan Oil', 'Rose Water', 'Aloe Vera'],
      routine: 'Morning & Evening',
      category: 'cleanser',
      skinType: ['all', 'dry', 'sensitive'],
      concerns: ['dryness', 'sensitivity']
    },
    {
      id: 2,
      name: 'Rose Water Balancing Toner',
      price: 18.99,
      rating: 4.6,
      image: 'https://images.pexels.com/photos/7262396/pexels-photo-7262396.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
      benefits: ['Balances pH', 'Minimizes pores', 'Soothes irritation'],
      ingredients: ['Rose Water', 'Witch Hazel', 'Glycerin'],
      routine: 'Evening',
      category: 'toner',
      skinType: ['all', 'oily', 'combination'],
      concerns: ['large-pores', 'oiliness']
    },
    {
      id: 3,
      name: 'Olive Oil Moisturizer',
      price: 32.99,
      rating: 4.9,
      image: 'https://images.pexels.com/photos/4465421/pexels-photo-4465421.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
      benefits: ['Deep hydration', 'Anti-aging', 'Natural ingredients'],
      ingredients: ['Olive Oil', 'Shea Butter', 'Vitamin E'],
      routine: 'Morning & Evening',
      category: 'moisturizer',
      skinType: ['dry', 'combination', 'sensitive'],
      concerns: ['dryness', 'aging']
    },
    {
      id: 4,
      name: 'Vitamin C Brightening Serum',
      price: 45.99,
      rating: 4.7,
      image: 'https://images.pexels.com/photos/5938567/pexels-photo-5938567.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
      benefits: ['Brightens skin', 'Reduces dark spots', 'Antioxidant protection'],
      ingredients: ['Vitamin C', 'Hyaluronic Acid', 'Niacinamide'],
      routine: 'Morning',
      category: 'serum',
      skinType: ['all', 'oily', 'combination'],
      concerns: ['dark-spots', 'dullness']
    },
    {
      id: 5,
      name: 'Tea Tree Acne Treatment',
      price: 21.99,
      rating: 4.5,
      image: 'https://images.pexels.com/photos/6621374/pexels-photo-6621374.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
      benefits: ['Fights acne', 'Reduces inflammation', 'Natural antibacterial'],
      ingredients: ['Tea Tree Oil', 'Salicylic Acid', 'Zinc'],
      routine: 'Evening',
      category: 'treatment',
      skinType: ['oily', 'combination'],
      concerns: ['acne', 'blemishes']
    },
    {
      id: 6,
      name: 'Prickly Pear Anti-Aging Cream',
      price: 55.99,
      rating: 4.8,
      image: 'https://images.pexels.com/photos/4465829/pexels-photo-4465829.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
      benefits: ['Reduces wrinkles', 'Firms skin', 'Rich in antioxidants'],
      ingredients: ['Prickly Pear Oil', 'Peptides', 'Retinol'],
      routine: 'Evening',
      category: 'treatment',
      skinType: ['all', 'dry', 'combination'],
      concerns: ['aging', 'wrinkles']
    },
  ];

  const filteredProducts = allProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.benefits.some(benefit => benefit.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    
    const matchesSkinType = selectedSkinType === 'all' || product.skinType.includes(selectedSkinType);
    
    const matchesPrice = priceRange === 'all' || 
                        (priceRange === 'under-25' && product.price < 25) ||
                        (priceRange === '25-50' && product.price >= 25 && product.price <= 50) ||
                        (priceRange === 'over-50' && product.price > 50);
    
    return matchesSearch && matchesCategory && matchesSkinType && matchesPrice;
  });

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#8B5CF6', '#7C3AED']}
        style={styles.header}
      >
        <Text style={styles.title}>Products</Text>
        <Text style={styles.subtitle}>
          Discover Tunisian bio-cosmetics for your skin
        </Text>
        
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Search size={20} color="#6B7280" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search products..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#9CA3AF"
            />
          </View>
          
          <TouchableOpacity 
            style={[styles.filterButton, showFilters && styles.filterButtonActive]}
            onPress={() => setShowFilters(!showFilters)}
          >
            <Filter size={20} color={showFilters ? '#FFFFFF' : '#8B5CF6'} />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {showFilters && (
          <View style={styles.filtersContainer}>
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Category</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.filterOptions}>
                  {categories.map(category => (
                    <TouchableOpacity
                      key={category.id}
                      style={[styles.filterChip, selectedCategory === category.id && styles.filterChipActive]}
                      onPress={() => setSelectedCategory(category.id)}
                    >
                      <Text style={[styles.filterChipText, selectedCategory === category.id && styles.filterChipTextActive]}>
                        {category.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>

            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Skin Type</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.filterOptions}>
                  {skinTypes.map(skinType => (
                    <TouchableOpacity
                      key={skinType.id}
                      style={[styles.filterChip, selectedSkinType === skinType.id && styles.filterChipActive]}
                      onPress={() => setSelectedSkinType(skinType.id)}
                    >
                      <Text style={[styles.filterChipText, selectedSkinType === skinType.id && styles.filterChipTextActive]}>
                        {skinType.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>

            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Price Range</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.filterOptions}>
                  {priceRanges.map(range => (
                    <TouchableOpacity
                      key={range.id}
                      style={[styles.filterChip, priceRange === range.id && styles.filterChipActive]}
                      onPress={() => setPriceRange(range.id)}
                    >
                      <Text style={[styles.filterChipText, priceRange === range.id && styles.filterChipTextActive]}>
                        {range.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>
          </View>
        )}

        <View style={styles.resultsHeader}>
          <Text style={styles.resultsCount}>
            {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} found
          </Text>
          
          <View style={styles.viewToggle}>
            <TouchableOpacity
              style={[styles.viewButton, viewMode === 'grid' && styles.viewButtonActive]}
              onPress={() => setViewMode('grid')}
            >
              <Grid size={16} color={viewMode === 'grid' ? '#FFFFFF' : '#6B7280'} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.viewButton, viewMode === 'list' && styles.viewButtonActive]}
              onPress={() => setViewMode('list')}
            >
              <List size={16} color={viewMode === 'list' ? '#FFFFFF' : '#6B7280'} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={viewMode === 'grid' ? styles.productsGrid : styles.productsList}>
          {filteredProducts.map(product => (
            <View key={product.id} style={viewMode === 'grid' ? styles.gridItem : styles.listItem}>
              <ProductCard
                product={product}
                onPress={() => {
                  // Navigate to product details
                }}
                onAddToCart={() => {
                  // Add to cart logic
                }}
              />
            </View>
          ))}
        </View>

        {filteredProducts.length === 0 && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyTitle}>No products found</Text>
            <Text style={styles.emptyText}>
              Try adjusting your search or filter criteria
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 24,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#E0E7FF',
    textAlign: 'center',
    marginBottom: 24,
  },
  searchContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
  },
  filterButton: {
    width: 48,
    height: 48,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#8B5CF6',
  },
  content: {
    flex: 1,
  },
  filtersContainer: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  filterSection: {
    marginBottom: 20,
  },
  filterTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  filterOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  filterChip: {
    backgroundColor: '#F9FAFB',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  filterChipActive: {
    backgroundColor: '#14B8A6',
    borderColor: '#0D9488',
  },
  filterChipText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  filterChipTextActive: {
    color: '#FFFFFF',
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  resultsCount: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  viewToggle: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 2,
  },
  viewButton: {
    padding: 8,
    borderRadius: 6,
  },
  viewButtonActive: {
    backgroundColor: '#14B8A6',
  },
  productsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    gap: 8,
  },
  productsList: {
    paddingHorizontal: 24,
  },
  gridItem: {
    width: '48%',
  },
  listItem: {
    width: '100%',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
});