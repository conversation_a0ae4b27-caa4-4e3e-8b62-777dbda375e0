import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Alert, Modal } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Sun, Moon, CircleCheck as CheckCircle, Clock, Bell, Calendar, TrendingUp, Award, Target, Info, X, Camera, RotateCcw, Share2 } from 'lucide-react-native';

interface RoutineStep {
  id: number;
  name: string;
  duration: string;
  description: string;
  productImage: string;
  instructions: string[];
  tips: string[];
  importance: 'essential' | 'recommended' | 'optional';
}

interface ProgressData {
  date: string;
  completedSteps: number[];
  totalSteps: number;
  timeOfDay: 'morning' | 'evening';
  notes?: string;
  photoUrl?: string;
}

export default function RoutineScreen() {
  const [selectedTime, setSelectedTime] = useState<'morning' | 'evening'>('morning');
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [currentStreak, setCurrentStreak] = useState(7);
  const [weeklyProgress, setWeeklyProgress] = useState([85, 92, 78, 95, 88, 100, 90]);
  const [selectedStep, setSelectedStep] = useState<RoutineStep | null>(null);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [showStepDetails, setShowStepDetails] = useState(false);
  const [reminderTime, setReminderTime] = useState({ morning: '08:00', evening: '21:00' });

  const routineSteps: Record<'morning' | 'evening', RoutineStep[]> = {
    morning: [
      {
        id: 1,
        name: 'Gentle Argan Cleanser',
        duration: '2 min',
        description: 'Start your day with a gentle cleanse',
        productImage: 'https://images.pexels.com/photos/4465831/pexels-photo-4465831.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop',
        instructions: [
          'Wet your face with lukewarm water',
          'Apply a small amount to your palms',
          'Gently massage in circular motions for 30 seconds',
          'Rinse thoroughly with cool water'
        ],
        tips: [
          'Use lukewarm water to avoid stripping natural oils',
          'Pat dry with a clean towel, don\'t rub'
        ],
        importance: 'essential'
      },
      {
        id: 2,
        name: 'Vitamin C Serum',
        duration: '1 min',
        description: 'Brighten and protect your skin',
        productImage: 'https://images.pexels.com/photos/5938567/pexels-photo-5938567.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop',
        instructions: [
          'Apply 2-3 drops to clean skin',
          'Gently pat into face and neck',
          'Wait 2-3 minutes before next step'
        ],
        tips: [
          'Always apply to clean, dry skin',
          'Start with every other day if you have sensitive skin'
        ],
        importance: 'recommended'
      },
      {
        id: 3,
        name: 'Olive Oil Moisturizer',
        duration: '1 min',
        description: 'Hydrate and nourish your skin',
        productImage: 'https://images.pexels.com/photos/4465421/pexels-photo-4465421.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop',
        instructions: [
          'Apply a dime-sized amount to face',
          'Massage upward in gentle strokes',
          'Don\'t forget your neck and décolletage'
        ],
        tips: [
          'Less is more - start with a small amount',
          'Perfect for Tunisia\'s dry climate'
        ],
        importance: 'essential'
      },
      {
        id: 4,
        name: 'Sunscreen SPF 30+',
        duration: '1 min',
        description: 'Essential protection for Tunisian sun',
        productImage: 'https://images.pexels.com/photos/4465832/pexels-photo-4465832.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop',
        instructions: [
          'Apply generously to all exposed areas',
          'Reapply every 2 hours when outdoors',
          'Don\'t forget ears, neck, and hands'
        ],
        tips: [
          'Apply 15 minutes before sun exposure',
          'Essential year-round in Tunisia'
        ],
        importance: 'essential'
      },
    ],
    evening: [
      {
        id: 1,
        name: 'Oil Cleanser',
        duration: '2 min',
        description: 'Remove makeup and daily impurities',
        productImage: 'https://images.pexels.com/photos/6621374/pexels-photo-6621374.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop',
        instructions: [
          'Apply to dry skin with dry hands',
          'Massage for 1 minute to dissolve makeup',
          'Add water to emulsify, then rinse'
        ],
        tips: [
          'Don\'t skip even if you don\'t wear makeup',
          'Helps remove sunscreen and pollution'
        ],
        importance: 'essential'
      },
      {
        id: 2,
        name: 'Gentle Argan Cleanser',
        duration: '2 min',
        description: 'Second cleanse for deep cleaning',
        productImage: 'https://images.pexels.com/photos/4465831/pexels-photo-4465831.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop',
        instructions: [
          'Apply to damp skin',
          'Massage gently for 30 seconds',
          'Rinse with cool water'
        ],
        tips: [
          'This ensures all impurities are removed',
          'Your skin should feel clean but not tight'
        ],
        importance: 'essential'
      },
      {
        id: 3,
        name: 'Rose Water Toner',
        duration: '30 sec',
        description: 'Balance and prepare skin',
        productImage: 'https://images.pexels.com/photos/7262396/pexels-photo-7262396.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop',
        instructions: [
          'Apply to cotton pad or spray directly',
          'Gently pat into skin',
          'Let absorb for 30 seconds'
        ],
        tips: [
          'Can be used throughout the day for refreshing',
          'Store in fridge for extra cooling effect'
        ],
        importance: 'recommended'
      },
      {
        id: 4,
        name: 'Retinol Serum',
        duration: '1 min',
        description: 'Anti-aging and skin renewal',
        productImage: 'https://images.pexels.com/photos/4465829/pexels-photo-4465829.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop',
        instructions: [
          'Apply 2-3 drops to clean skin',
          'Avoid eye area',
          'Wait 5 minutes before moisturizer'
        ],
        tips: [
          'Start 2-3 times per week',
          'Always use sunscreen the next day'
        ],
        importance: 'recommended'
      },
      {
        id: 5,
        name: 'Night Moisturizer',
        duration: '1 min',
        description: 'Deep overnight nourishment',
        productImage: 'https://images.pexels.com/photos/4465421/pexels-photo-4465421.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop',
        instructions: [
          'Apply generous amount to face and neck',
          'Massage in upward motions',
          'Let absorb completely'
        ],
        tips: [
          'Richer formula for overnight repair',
          'Perfect time for active ingredients to work'
        ],
        importance: 'essential'
      },
    ],
  };

  const currentSteps = routineSteps[selectedTime];
  const completedCount = currentSteps.filter(step => completedSteps.has(step.id)).length;
  const completionPercentage = (completedCount / currentSteps.length) * 100;

  const toggleStep = (stepId: number) => {
    const newCompleted = new Set(completedSteps);
    if (newCompleted.has(stepId)) {
      newCompleted.delete(stepId);
    } else {
      newCompleted.add(stepId);
    }
    setCompletedSteps(newCompleted);

    // Check if routine is completed
    if (newCompleted.size === currentSteps.length && completedCount < currentSteps.length) {
      Alert.alert(
        '🎉 Routine Complete!',
        `Great job completing your ${selectedTime} routine! Keep up the consistency.`,
        [{ text: 'Awesome!', style: 'default' }]
      );
    }
  };

  const resetRoutine = () => {
    Alert.alert(
      'Reset Routine',
      'Are you sure you want to reset today\'s progress?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: () => setCompletedSteps(new Set())
        }
      ]
    );
  };

  const shareProgress = () => {
    Alert.alert(
      'Share Progress',
      `I'm ${currentStreak} days into my skincare routine with ${Math.round(completionPercentage)}% completion today! 🌟`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Share', onPress: () => console.log('Sharing progress...') }
      ]
    );
  };

  const getStepIcon = (step: RoutineStep) => {
    switch (step.importance) {
      case 'essential':
        return <Target size={16} color="#EF4444" />;
      case 'recommended':
        return <TrendingUp size={16} color="#F59E0B" />;
      case 'optional':
        return <Info size={16} color="#6B7280" />;
      default:
        return null;
    }
  };

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'essential': return '#EF4444';
      case 'recommended': return '#F59E0B';
      case 'optional': return '#6B7280';
      default: return '#6B7280';
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <LinearGradient
        colors={['#14B8A6', '#0D9488']}
        style={styles.header}
      >
        <Text style={styles.title}>My Routine</Text>
        <Text style={styles.subtitle}>Your personalized skincare journey</Text>
        
        {/* Streak Counter */}
        <View style={styles.streakContainer}>
          <Award size={24} color="#FFFFFF" />
          <Text style={styles.streakText}>{currentStreak} Day Streak!</Text>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        {/* Progress Overview */}
        <View style={styles.progressCard}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressTitle}>Today's Progress</Text>
            <TouchableOpacity onPress={() => setShowProgressModal(true)}>
              <Calendar size={20} color="#14B8A6" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.progressBar}>
            <LinearGradient
              colors={['#14B8A6', '#0D9488']}
              style={[styles.progressFill, { width: `${completionPercentage}%` }]}
            />
          </View>
          
          <View style={styles.progressStats}>
            <Text style={styles.progressText}>
              {completedCount} of {currentSteps.length} steps completed
            </Text>
            <Text style={styles.progressPercentage}>
              {Math.round(completionPercentage)}%
            </Text>
          </View>

          {/* Weekly Progress Chart */}
          <View style={styles.weeklyChart}>
            <Text style={styles.chartTitle}>This Week</Text>
            <View style={styles.chartBars}>
              {weeklyProgress.map((progress, index) => (
                <View key={index} style={styles.chartDay}>
                  <View style={styles.chartBar}>
                    <View 
                      style={[
                        styles.chartBarFill, 
                        { height: `${progress}%` }
                      ]} 
                    />
                  </View>
                  <Text style={styles.chartDayLabel}>
                    {['S', 'M', 'T', 'W', 'T', 'F', 'S'][index]}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Time Selector */}
        <View style={styles.timeSelector}>
          <TouchableOpacity
            style={[styles.timeButton, selectedTime === 'morning' && styles.timeButtonActive]}
            onPress={() => setSelectedTime('morning')}
          >
            <Sun size={20} color={selectedTime === 'morning' ? '#FFFFFF' : '#14B8A6'} />
            <Text style={[styles.timeButtonText, selectedTime === 'morning' && styles.timeButtonTextActive]}>
              Morning
            </Text>
            <Text style={[styles.timeButtonSubtext, selectedTime === 'morning' && styles.timeButtonSubtextActive]}>
              {reminderTime.morning}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.timeButton, selectedTime === 'evening' && styles.timeButtonActive]}
            onPress={() => setSelectedTime('evening')}
          >
            <Moon size={20} color={selectedTime === 'evening' ? '#FFFFFF' : '#14B8A6'} />
            <Text style={[styles.timeButtonText, selectedTime === 'evening' && styles.timeButtonTextActive]}>
              Evening
            </Text>
            <Text style={[styles.timeButtonSubtext, selectedTime === 'evening' && styles.timeButtonSubtextActive]}>
              {reminderTime.evening}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Routine Steps */}
        <View style={styles.stepsContainer}>
          {currentSteps.map((step, index) => (
            <TouchableOpacity
              key={step.id}
              style={[styles.stepCard, completedSteps.has(step.id) && styles.stepCardCompleted]}
              onPress={() => toggleStep(step.id)}
              onLongPress={() => {
                setSelectedStep(step);
                setShowStepDetails(true);
              }}
            >
              <View style={styles.stepHeader}>
                <View style={styles.stepLeft}>
                  <View style={[styles.stepNumber, completedSteps.has(step.id) && styles.stepNumberCompleted]}>
                    {completedSteps.has(step.id) ? (
                      <CheckCircle size={20} color="#FFFFFF" />
                    ) : (
                      <Text style={styles.stepNumberText}>{index + 1}</Text>
                    )}
                  </View>
                  
                  <Image source={{ uri: step.productImage }} style={styles.productImage} />
                  
                  <View style={styles.stepInfo}>
                    <View style={styles.stepTitleRow}>
                      <Text style={[styles.stepName, completedSteps.has(step.id) && styles.stepNameCompleted]}>
                        {step.name}
                      </Text>
                      {getStepIcon(step)}
                    </View>
                    <Text style={styles.stepDescription}>{step.description}</Text>
                    <View style={styles.stepMeta}>
                      <Clock size={12} color="#6B7280" />
                      <Text style={styles.stepDuration}>{step.duration}</Text>
                      <View style={[styles.importanceBadge, { backgroundColor: getImportanceColor(step.importance) }]}>
                        <Text style={styles.importanceText}>{step.importance}</Text>
                      </View>
                    </View>
                  </View>
                </View>

                <TouchableOpacity
                  style={styles.infoButton}
                  onPress={() => {
                    setSelectedStep(step);
                    setShowStepDetails(true);
                  }}
                >
                  <Info size={16} color="#6B7280" />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={resetRoutine}>
            <RotateCcw size={18} color="#6B7280" />
            <Text style={styles.actionButtonText}>Reset</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={shareProgress}>
            <Share2 size={18} color="#6B7280" />
            <Text style={styles.actionButtonText}>Share</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton}>
            <Camera size={18} color="#6B7280" />
            <Text style={styles.actionButtonText}>Progress Photo</Text>
          </TouchableOpacity>
        </View>

        {/* Reminder Button */}
        <TouchableOpacity style={styles.reminderButton}>
          <LinearGradient
            colors={['#8B5CF6', '#7C3AED']}
            style={styles.reminderGradient}
          >
            <Bell size={20} color="#FFFFFF" />
            <Text style={styles.reminderText}>Manage Reminders</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>

      {/* Step Details Modal */}
      <Modal visible={showStepDetails} animationType="slide" presentationStyle="pageSheet">
        {selectedStep && (
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{selectedStep.name}</Text>
              <TouchableOpacity onPress={() => setShowStepDetails(false)}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <Image source={{ uri: selectedStep.productImage }} style={styles.modalProductImage} />
              
              <Text style={styles.modalDescription}>{selectedStep.description}</Text>

              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>How to Use</Text>
                {selectedStep.instructions.map((instruction, index) => (
                  <View key={index} style={styles.instructionItem}>
                    <View style={styles.instructionNumber}>
                      <Text style={styles.instructionNumberText}>{index + 1}</Text>
                    </View>
                    <Text style={styles.instructionText}>{instruction}</Text>
                  </View>
                ))}
              </View>

              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Pro Tips</Text>
                {selectedStep.tips.map((tip, index) => (
                  <View key={index} style={styles.tipItem}>
                    <View style={styles.tipBullet} />
                    <Text style={styles.tipText}>{tip}</Text>
                  </View>
                ))}
              </View>

              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Importance Level</Text>
                <View style={styles.importanceContainer}>
                  <View style={[styles.importanceBadge, { backgroundColor: getImportanceColor(selectedStep.importance) }]}>
                    <Text style={styles.importanceText}>{selectedStep.importance}</Text>
                  </View>
                  <Text style={styles.importanceDescription}>
                    {selectedStep.importance === 'essential' && 'Critical for your routine success'}
                    {selectedStep.importance === 'recommended' && 'Highly beneficial for your skin goals'}
                    {selectedStep.importance === 'optional' && 'Nice to have when you have extra time'}
                  </Text>
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, completedSteps.has(selectedStep.id) && styles.modalButtonCompleted]}
                onPress={() => {
                  toggleStep(selectedStep.id);
                  setShowStepDetails(false);
                }}
              >
                <Text style={[styles.modalButtonText, completedSteps.has(selectedStep.id) && styles.modalButtonTextCompleted]}>
                  {completedSteps.has(selectedStep.id) ? 'Mark as Incomplete' : 'Mark as Complete'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </Modal>

      {/* Progress History Modal */}
      <Modal visible={showProgressModal} animationType="slide" presentationStyle="pageSheet">
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Progress History</Text>
            <TouchableOpacity onPress={() => setShowProgressModal(false)}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>{currentStreak}</Text>
                <Text style={styles.statLabel}>Day Streak</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>
                  {Math.round(weeklyProgress.reduce((a, b) => a + b, 0) / weeklyProgress.length)}%
                </Text>
                <Text style={styles.statLabel}>Weekly Avg</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>28</Text>
                <Text style={styles.statLabel}>Days Active</Text>
              </View>
            </View>

            <View style={styles.progressHistory}>
              <Text style={styles.historyTitle}>Recent Activity</Text>
              {[...Array(7)].map((_, index) => (
                <View key={index} style={styles.historyItem}>
                  <View style={styles.historyDate}>
                    <Text style={styles.historyDateText}>
                      {new Date(Date.now() - index * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                      })}
                    </Text>
                  </View>
                  <View style={styles.historyProgress}>
                    <View style={styles.historyBar}>
                      <View 
                        style={[
                          styles.historyBarFill, 
                          { width: `${weeklyProgress[6 - index] || 0}%` }
                        ]} 
                      />
                    </View>
                    <Text style={styles.historyPercentage}>
                      {weeklyProgress[6 - index] || 0}%
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </ScrollView>
        </View>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 32,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#E0F2FE',
    textAlign: 'center',
    marginBottom: 16,
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  streakText: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  content: {
    padding: 24,
  },
  progressCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  progressBar: {
    height: 12,
    backgroundColor: '#E5E7EB',
    borderRadius: 6,
    marginBottom: 12,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 6,
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  progressPercentage: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#14B8A6',
  },
  weeklyChart: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 16,
  },
  chartTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginBottom: 12,
    textAlign: 'center',
  },
  chartBars: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 60,
  },
  chartDay: {
    alignItems: 'center',
    flex: 1,
  },
  chartBar: {
    width: 20,
    height: 40,
    backgroundColor: '#F3F4F6',
    borderRadius: 10,
    marginBottom: 8,
    overflow: 'hidden',
  },
  chartBarFill: {
    backgroundColor: '#14B8A6',
    borderRadius: 10,
    position: 'absolute',
    bottom: 0,
    width: '100%',
  },
  chartDayLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#9CA3AF',
  },
  timeSelector: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 4,
    marginBottom: 24,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  timeButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 4,
  },
  timeButtonActive: {
    backgroundColor: '#14B8A6',
  },
  timeButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#14B8A6',
  },
  timeButtonTextActive: {
    color: '#FFFFFF',
  },
  timeButtonSubtext: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  timeButtonSubtextActive: {
    color: '#E0F2FE',
  },
  stepsContainer: {
    gap: 16,
    marginBottom: 24,
  },
  stepCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  stepCardCompleted: {
    borderColor: '#10B981',
    backgroundColor: '#F0FDF4',
  },
  stepHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  stepLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#14B8A6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumberCompleted: {
    backgroundColor: '#10B981',
  },
  stepNumberText: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  productImage: {
    width: 48,
    height: 48,
    borderRadius: 12,
    marginRight: 12,
  },
  stepInfo: {
    flex: 1,
  },
  stepTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  stepName: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    flex: 1,
  },
  stepNameCompleted: {
    color: '#6B7280',
  },
  stepDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 8,
  },
  stepMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepDuration: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  importanceBadge: {
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  importanceText: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    textTransform: 'uppercase',
  },
  infoButton: {
    padding: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingVertical: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  actionButton: {
    alignItems: 'center',
    gap: 4,
  },
  actionButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  reminderButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  reminderGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 12,
  },
  reminderText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    paddingTop: 60,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  modalContent: {
    flex: 1,
    padding: 24,
  },
  modalProductImage: {
    width: '100%',
    height: 200,
    borderRadius: 16,
    marginBottom: 20,
  },
  modalDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 24,
    marginBottom: 24,
    textAlign: 'center',
  },
  modalSection: {
    marginBottom: 24,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 16,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  instructionNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#14B8A6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  instructionNumberText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  instructionText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 20,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  tipBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#14B8A6',
    marginTop: 6,
    marginRight: 12,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 20,
  },
  importanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  importanceDescription: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  modalFooter: {
    padding: 24,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  modalButton: {
    backgroundColor: '#14B8A6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  modalButtonCompleted: {
    backgroundColor: '#EF4444',
  },
  modalButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  modalButtonTextCompleted: {
    color: '#FFFFFF',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
    elevation: 1,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statNumber: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#14B8A6',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
  },
  progressHistory: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
  },
  historyTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 16,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  historyDate: {
    width: 60,
  },
  historyDateText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  historyProgress: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  historyBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 4,
    overflow: 'hidden',
  },
  historyBarFill: {
    height: '100%',
    backgroundColor: '#14B8A6',
    borderRadius: 4,
  },
  historyPercentage: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#14B8A6',
    width: 35,
    textAlign: 'right',
  },
});