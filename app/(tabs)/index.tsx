import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { Sparkles, Camera, Users, TrendingUp, Search, ArrowRight } from 'lucide-react-native';

export default function HomeScreen() {
  const handleSkinAnalysis = () => {
    router.push({
      pathname: '/diagnosis',
      params: { type: 'skin' }
    });
  };

  const handleHairAnalysis = () => {
    router.push({
      pathname: '/diagnosis',
      params: { type: 'hair' }
    });
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <LinearGradient
        colors={['#14B8A6', '#0D9488']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.greeting}>Welcome back!</Text>
          <Text style={styles.subtitle}>Discover your perfect skincare routine</Text>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.analysisSection}>
          <Text style={styles.sectionTitle}>AI Analysis</Text>
          <Text style={styles.sectionSubtitle}>
            Get personalized recommendations in under 2 minutes
          </Text>
          
          <View style={styles.analysisGrid}>
            <TouchableOpacity style={styles.analysisCard} onPress={handleSkinAnalysis}>
              <LinearGradient
                colors={['#14B8A6', '#0D9488']}
                style={styles.analysisGradient}
              >
                <Sparkles size={28} color="#FFFFFF" />
                <Text style={styles.analysisTitle}>Skin Analysis</Text>
                <Text style={styles.analysisSubtitle}>
                  Acne, wrinkles, pigmentation
                </Text>
                <ArrowRight size={16} color="#FFFFFF" style={styles.analysisArrow} />
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity style={styles.analysisCard} onPress={handleHairAnalysis}>
              <LinearGradient
                colors={['#EC4899', '#BE185D']}
                style={styles.analysisGradient}
              >
                <Camera size={28} color="#FFFFFF" />
                <Text style={styles.analysisTitle}>Hair Analysis</Text>
                <Text style={styles.analysisSubtitle}>
                  Dandruff, dryness, hair loss
                </Text>
                <ArrowRight size={16} color="#FFFFFF" style={styles.analysisArrow} />
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Featured Products</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.productScroll}>
            {featuredProducts.map((product, index) => (
              <TouchableOpacity key={index} style={styles.productCard}>
                <Image source={{ uri: product.image }} style={styles.productImage} />
                <Text style={styles.productName}>{product.name}</Text>
                <Text style={styles.productPrice}>${product.price}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <TouchableOpacity 
          style={styles.viewAllButton}
          onPress={() => router.push('/(tabs)/products')}
        >
          <View style={styles.viewAllContent}>
            <Search size={20} color="#14B8A6" />
            <Text style={styles.viewAllText}>View All Products</Text>
            <ArrowRight size={16} color="#14B8A6" />
          </View>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const featuredProducts = [
  {
    name: 'Argan Oil Serum',
    price: '24.99',
    image: 'https://images.pexels.com/photos/4465831/pexels-photo-4465831.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop'
  },
  {
    name: 'Rose Water Toner',
    price: '18.99',
    image: 'https://images.pexels.com/photos/7262396/pexels-photo-7262396.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop'
  },
  {
    name: 'Olive Moisturizer',
    price: '32.99',
    image: 'https://images.pexels.com/photos/4465421/pexels-photo-4465421.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop'
  },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 32,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    alignItems: 'center',
  },
  greeting: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#E0F2FE',
    textAlign: 'center',
  },
  content: {
    padding: 24,
  },
  analysisSection: {
    marginBottom: 32,
  },
  analysisGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  analysisCard: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
  },
  analysisGradient: {
    padding: 20,
    alignItems: 'center',
    minHeight: 140,
    justifyContent: 'center',
    position: 'relative',
  },
  analysisTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginTop: 12,
    marginBottom: 4,
    textAlign: 'center',
  },
  analysisSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 16,
  },
  analysisArrow: {
    position: 'absolute',
    top: 12,
    right: 12,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 20,
  },
  productScroll: {
    marginHorizontal: -24,
    paddingHorizontal: 24,
  },
  productCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 12,
    marginRight: 16,
    width: 140,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  productImage: {
    width: '100%',
    height: 100,
    borderRadius: 12,
    marginBottom: 8,
  },
  productName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#14B8A6',
  },
  viewAllButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  viewAllContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  viewAllText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#14B8A6',
    textAlign: 'center',
  },
});