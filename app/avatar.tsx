import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Target, ArrowRight } from 'lucide-react-native';

export default function AvatarScreen() {
  const { type, answers } = useLocalSearchParams();
  const [selectedAreas, setSelectedAreas] = useState<string[]>([]);

  const skinAreas = [
    { id: 'forehead', label: 'Forehead', x: 50, y: 20 },
    { id: 'nose', label: 'Nose', x: 50, y: 35 },
    { id: 'cheeks', label: 'Cheeks', x: 35, y: 40 },
    { id: 'chin', label: 'Chin', x: 50, y: 60 },
    { id: 'jawline', label: 'Jawline', x: 40, y: 65 },
  ];

  const hairAreas = [
    { id: 'scalp', label: 'Scalp', x: 50, y: 15 },
    { id: 'crown', label: 'Crown', x: 50, y: 25 },
    { id: 'temples', label: 'Temples', x: 25, y: 30 },
    { id: 'hairline', label: 'Hairline', x: 50, y: 20 },
  ];

  const areas = type === 'skin' ? skinAreas : hairAreas;

  const toggleArea = (areaId: string) => {
    setSelectedAreas(prev => 
      prev.includes(areaId) 
        ? prev.filter(id => id !== areaId)
        : [...prev, areaId]
    );
  };

  const handleContinue = () => {
    if (selectedAreas.length === 0) {
      Alert.alert('Select Areas', 'Please tap on the areas where you have concerns.');
      return;
    }

    router.push({
      pathname: '/results',
      params: { 
        type, 
        answers, 
        selectedAreas: JSON.stringify(selectedAreas) 
      }
    });
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={type === 'skin' ? ['#14B8A6', '#0D9488'] : ['#EC4899', '#BE185D']}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.title}>Mark Problem Areas</Text>
        <Text style={styles.subtitle}>
          Tap on the {type} areas where you have concerns
        </Text>
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.avatarContainer}>
          <View style={styles.avatarSilhouette}>
            {areas.map(area => (
              <TouchableOpacity
                key={area.id}
                style={[
                  styles.areaMarker,
                  {
                    left: `${area.x}%`,
                    top: `${area.y}%`,
                  },
                  selectedAreas.includes(area.id) && styles.areaMarkerSelected
                ]}
                onPress={() => toggleArea(area.id)}
              >
                <Target size={16} color={selectedAreas.includes(area.id) ? '#FFFFFF' : '#14B8A6'} />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.selectedAreasContainer}>
          <Text style={styles.selectedTitle}>Selected Areas:</Text>
          {selectedAreas.length > 0 ? (
            <View style={styles.selectedAreasList}>
              {selectedAreas.map(areaId => {
                const area = areas.find(a => a.id === areaId);
                return (
                  <View key={areaId} style={styles.selectedAreaTag}>
                    <Text style={styles.selectedAreaText}>{area?.label}</Text>
                  </View>
                );
              })}
            </View>
          ) : (
            <Text style={styles.noSelectionText}>No areas selected yet</Text>
          )}
        </View>
      </View>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.continueButton, selectedAreas.length === 0 && styles.continueButtonDisabled]}
          onPress={handleContinue}
        >
          <LinearGradient
            colors={selectedAreas.length > 0 ? ['#14B8A6', '#0D9488'] : ['#D1D5DB', '#D1D5DB']}
            style={styles.continueButtonGradient}
          >
            <Text style={[
              styles.continueButtonText,
              selectedAreas.length === 0 && styles.continueButtonTextDisabled
            ]}>
              Analyze with AI
            </Text>
            <ArrowRight size={20} color={selectedAreas.length > 0 ? '#FFFFFF' : '#9CA3AF'} />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 24,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  backButton: {
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#E0F2FE',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 24,
  },
  avatarContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  avatarSilhouette: {
    width: 200,
    height: 300,
    backgroundColor: '#FFFFFF',
    borderRadius: 100,
    position: 'relative',
    elevation: 4,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
  },
  areaMarker: {
    position: 'absolute',
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#14B8A6',
    transform: [{ translateX: -16 }, { translateY: -16 }],
  },
  areaMarkerSelected: {
    backgroundColor: '#14B8A6',
    borderColor: '#0D9488',
  },
  selectedAreasContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  selectedTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  selectedAreasList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectedAreaTag: {
    backgroundColor: '#F0FDFA',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#14B8A6',
  },
  selectedAreaText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#14B8A6',
  },
  noSelectionText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    fontStyle: 'italic',
  },
  footer: {
    padding: 24,
    paddingBottom: 32,
  },
  continueButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  continueButtonDisabled: {
    opacity: 0.6,
  },
  continueButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    gap: 12,
  },
  continueButtonText: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  continueButtonTextDisabled: {
    color: '#9CA3AF',
  },
});