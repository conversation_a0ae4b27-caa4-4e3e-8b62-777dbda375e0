import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { <PERSON>R<PERSON>, Spark<PERSON>, <PERSON>, Heart } from 'lucide-react-native';

export default function OnboardingScreen() {
  const handleGetStarted = () => {
    router.push('/(tabs)');
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <LinearGradient
        colors={['#14B8A6', '#0D9488']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Sparkles size={48} color="#FFFFFF" />
          <Text style={styles.title}>Welcome to SkinCare AI</Text>
          <Text style={styles.subtitle}>
            Discover your perfect skincare routine with AI-powered analysis
          </Text>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.featureCard}>
          <Shield size={32} color="#14B8A6" />
          <Text style={styles.featureTitle}>AI-Powered Analysis</Text>
          <Text style={styles.featureText}>
            Get personalized recommendations based on your skin type and concerns
          </Text>
        </View>

        <View style={styles.featureCard}>
          <Heart size={32} color="#EC4899" />
          <Text style={styles.featureTitle}>Natural Ingredients</Text>
          <Text style={styles.featureText}>
            Discover Tunisian bio-cosmetics with proven natural ingredients
          </Text>
        </View>

        <View style={styles.featureCard}>
          <Sparkles size={32} color="#8B5CF6" />
          <Text style={styles.featureTitle}>Personalized Routine</Text>
          <Text style={styles.featureText}>
            Follow a custom routine designed specifically for your needs
          </Text>
        </View>
      </View>

      <View style={styles.footer}>
        <TouchableOpacity style={styles.getStartedButton} onPress={handleGetStarted}>
          <LinearGradient
            colors={['#14B8A6', '#0D9488']}
            style={styles.getStartedGradient}
          >
            <Text style={styles.getStartedText}>Get Started</Text>
            <ArrowRight size={20} color="#FFFFFF" />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 80,
    paddingHorizontal: 24,
    paddingBottom: 40,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#E0F2FE',
    textAlign: 'center',
    lineHeight: 24,
  },
  content: {
    padding: 24,
    gap: 24,
  },
  featureCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  featureTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  featureText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  footer: {
    padding: 24,
    paddingBottom: 40,
  },
  getStartedButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  getStartedGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    gap: 12,
  },
  getStartedText: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
});