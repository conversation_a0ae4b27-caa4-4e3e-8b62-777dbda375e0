import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Sparkles, Clock, ShoppingCart, Star } from 'lucide-react-native';

export default function ResultsScreen() {
  const { type, answers, selectedAreas } = useLocalSearchParams();
  const [cart, setCart] = useState<number[]>([]);

  // Mock AI analysis results
  const analysisResult = {
    condition: type === 'skin' ? 'Combination Skin with Acne Concerns' : 'Dry Scalp with Hair Thinning',
    confidence: 92,
    description: type === 'skin' 
      ? 'Based on your responses and selected areas, you have combination skin with mild acne primarily in the T-zone. Your skin needs gentle cleansing, balanced hydration, and targeted acne treatment.'
      : 'Your analysis indicates dry scalp conditions with early signs of hair thinning. A moisturizing routine with scalp stimulation will help improve hair health.',
    severity: 'Mild to Moderate'
  };

  const recommendedProducts = [
    {
      id: 1,
      name: 'Gentle Argan Cleanser',
      price: 24.99,
      rating: 4.8,
      image: 'https://images.pexels.com/photos/4465831/pexels-photo-4465831.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
      benefits: ['Removes impurities', 'Hydrates skin', 'Anti-inflammatory'],
      routine: 'Morning & Evening'
    },
    {
      id: 2,
      name: 'Rose Water Balancing Toner',
      price: 18.99,
      rating: 4.6,
      image: 'https://images.pexels.com/photos/7262396/pexels-photo-7262396.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
      benefits: ['Balances pH', 'Minimizes pores', 'Soothes irritation'],
      routine: 'Evening'
    },
    {
      id: 3,
      name: 'Olive Oil Moisturizer',
      price: 32.99,
      rating: 4.9,
      image: 'https://images.pexels.com/photos/4465421/pexels-photo-4465421.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop',
      benefits: ['Deep hydration', 'Anti-aging', 'Natural ingredients'],
      routine: 'Morning & Evening'
    },
  ];

  const addToCart = (productId: number) => {
    setCart(prev => [...prev, productId]);
  };

  const removeFromCart = (productId: number) => {
    setCart(prev => prev.filter(id => id !== productId));
  };

  const isInCart = (productId: number) => cart.includes(productId);

  const handleCheckout = () => {
    if (cart.length === 0) {
      Alert.alert('Empty Cart', 'Please add some products to your cart first.');
      return;
    }
    router.push({
      pathname: '/checkout',
      params: { productIds: JSON.stringify(cart) }
    });
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <LinearGradient
        colors={['#8B5CF6', '#7C3AED']}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.title}>Analysis Results</Text>
        <View style={styles.confidenceContainer}>
          <Sparkles size={20} color="#FFFFFF" />
          <Text style={styles.confidenceText}>{analysisResult.confidence}% Confidence</Text>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.resultCard}>
          <Text style={styles.conditionTitle}>{analysisResult.condition}</Text>
          <Text style={styles.severityText}>Severity: {analysisResult.severity}</Text>
          <Text style={styles.descriptionText}>{analysisResult.description}</Text>
        </View>

        <Text style={styles.sectionTitle}>Recommended Products</Text>
        
        {recommendedProducts.map(product => (
          <View key={product.id} style={styles.productCard}>
            <Image source={{ uri: product.image }} style={styles.productImage} />
            
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{product.name}</Text>
              <View style={styles.ratingContainer}>
                <Star size={16} color="#F59E0B" />
                <Text style={styles.ratingText}>{product.rating}</Text>
              </View>
              <Text style={styles.productPrice}>${product.price}</Text>
              
              <View style={styles.benefitsContainer}>
                {product.benefits.map((benefit, index) => (
                  <View key={index} style={styles.benefitTag}>
                    <Text style={styles.benefitText}>{benefit}</Text>
                  </View>
                ))}
              </View>

              <View style={styles.routineInfo}>
                <Clock size={14} color="#6B7280" />
                <Text style={styles.routineText}>{product.routine}</Text>
              </View>

              <TouchableOpacity
                style={[styles.addToCartButton, isInCart(product.id) && styles.removeFromCartButton]}
                onPress={() => isInCart(product.id) ? removeFromCart(product.id) : addToCart(product.id)}
              >
                <Text style={[styles.addToCartText, isInCart(product.id) && styles.removeFromCartText]}>
                  {isInCart(product.id) ? 'Remove from Cart' : 'Add to Cart'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </View>

      {cart.length > 0 && (
        <View style={styles.footer}>
          <TouchableOpacity style={styles.checkoutButton} onPress={handleCheckout}>
            <LinearGradient
              colors={['#14B8A6', '#0D9488']}
              style={styles.checkoutButtonGradient}
            >
              <ShoppingCart size={20} color="#FFFFFF" />
              <Text style={styles.checkoutButtonText}>
                Checkout ({cart.length} item{cart.length > 1 ? 's' : ''})
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 24,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  backButton: {
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 12,
    textAlign: 'center',
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  confidenceText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#E0E7FF',
  },
  content: {
    padding: 24,
  },
  resultCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    elevation: 4,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
  },
  conditionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  severityText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#F59E0B',
    marginBottom: 16,
    textAlign: 'center',
  },
  descriptionText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 24,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 20,
  },
  productCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 20,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  productImage: {
    width: '100%',
    height: 150,
  },
  productInfo: {
    padding: 20,
  },
  productName: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 8,
  },
  ratingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
  },
  productPrice: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#14B8A6',
    marginBottom: 12,
  },
  benefitsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 12,
  },
  benefitTag: {
    backgroundColor: '#F0FDFA',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  benefitText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#14B8A6',
  },
  routineInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 16,
  },
  routineText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  addToCartButton: {
    backgroundColor: '#14B8A6',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  removeFromCartButton: {
    backgroundColor: '#EF4444',
  },
  addToCartText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  removeFromCartText: {
    color: '#FFFFFF',
  },
  footer: {
    padding: 24,
    paddingBottom: 32,
  },
  checkoutButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  checkoutButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    gap: 12,
  },
  checkoutButtonText: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
});