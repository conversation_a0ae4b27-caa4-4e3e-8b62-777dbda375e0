import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Camera, FileText, ArrowRight, Sparkles } from 'lucide-react-native';
import ImageCapture from '@/components/ImageCapture';
import AnalysisResults from '@/components/AnalysisResults';
import LoadingSpinner from '@/components/LoadingSpinner';
import useAIAnalysis from '@/hooks/useAIAnalysis';
import { ImageData, QuestionnaireData } from '@/services/gemini';
import { analytics } from '@/services/analytics';

export default function DiagnosisScreen() {
  const { type } = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showImageCapture, setShowImageCapture] = useState(false);
  const [capturedImage, setCapturedImage] = useState<ImageData | null>(null);
  const [showResults, setShowResults] = useState(false);
  
  const { isAnalyzing, result, error, analyzeCondition, clearResult } = useAIAnalysis();

  const skinQuestions = [
    {
      id: 'skin_type',
      question: 'What is your skin type?',
      options: ['Oily', 'Dry', 'Combination', 'Sensitive', 'Not sure']
    },
    {
      id: 'main_concern',
      question: 'What is your main skin concern?',
      options: ['Acne', 'Wrinkles', 'Dark spots', 'Dryness', 'Sensitivity']
    },
    {
      id: 'routine_experience',
      question: 'How experienced are you with skincare?',
      options: ['Beginner', 'Some experience', 'Very experienced']
    },
    {
      id: 'budget',
      question: 'What is your monthly skincare budget?',
      options: ['Under $50', '$50-100', '$100-200', 'Over $200']
    }
  ];

  const hairQuestions = [
    {
      id: 'hair_type',
      question: 'What is your hair type?',
      options: ['Straight', 'Wavy', 'Curly', 'Coily']
    },
    {
      id: 'scalp_condition',
      question: 'How would you describe your scalp?',
      options: ['Oily', 'Dry', 'Normal', 'Sensitive', 'Flaky']
    },
    {
      id: 'main_concern',
      question: 'What is your main hair concern?',
      options: ['Hair loss', 'Dandruff', 'Dryness', 'Damage', 'Lack of volume']
    },
    {
      id: 'washing_frequency',
      question: 'How often do you wash your hair?',
      options: ['Daily', '2-3 times a week', 'Once a week', 'Less than once a week']
    }
  ];

  const questions = type === 'skin' ? skinQuestions : hairQuestions;
  const currentQuestion = questions[currentStep];
  const isLastQuestion = currentStep === questions.length - 1;

  const handleAnswerSelect = (answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: answer
    }));
  };

  const handleNext = async () => {
    if (!answers[currentQuestion.id]) {
      Alert.alert('Please select an answer', 'You need to select an answer to continue.');
      return;
    }

    if (!isLastQuestion) {
      setCurrentStep(currentStep + 1);
    } else {
      // Last question - proceed to analysis
      await performAnalysis();
    }
  };

  const performAnalysis = async () => {
    try {
      // Convert answers to structured questionnaire data
      const questionnaireData: QuestionnaireData = {
        skinType: answers.skin_type as any,
        mainConcern: answers.main_concern,
        age: 25, // Would be collected in a real questionnaire
        lifestyle: {
          sunExposure: 'moderate',
          stressLevel: 'moderate',
          sleepQuality: 'good',
          diet: 'average',
          exercise: 'moderate'
        },
        // Hair-specific
        hairType: answers.hair_type as any,
        scalpCondition: answers.scalp_condition as any,
        hairConcerns: answers.main_concern ? [answers.main_concern] : [],
        washingFrequency: answers.washing_frequency as any
      };

      // For demo, use some selected areas based on the type
      const selectedAreas = type === 'skin' 
        ? ['forehead', 'nose', 'cheeks'] 
        : ['scalp', 'crown'];

      analytics.trackUserJourney('diagnosis_completed', {
        type,
        questionsAnswered: Object.keys(answers).length,
        hasImage: !!capturedImage
      });

      await analyzeCondition(
        type as 'skin' | 'hair',
        questionnaireData,
        selectedAreas,
        capturedImage || undefined
      );

      setShowResults(true);
    } catch (err) {
      Alert.alert('Analysis Error', 'Failed to analyze your condition. Please try again.');
    }
  };

  const handleImageCaptured = (image: ImageData) => {
    setCapturedImage(image);
    setShowImageCapture(false);
    analytics.trackEvent('image_captured', {
      type,
      quality: image.quality,
      fileSize: image.metadata.fileSize
    });
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const handleProductRecommendations = () => {
    if (result) {
      router.push({
        pathname: '/results'
      });
    }
  };

  const handleFeedback = (rating: number, feedback?: string) => {
    if (result) {
      analytics.trackUserFeedback(result.analysisId, rating, feedback);
    }
  };

  // Show results if analysis is complete
  if (showResults && result) {
    return (
      <AnalysisResults 
        result={result}
        onProductRecommendations={handleProductRecommendations}
        onFeedback={handleFeedback}
      />
    );
  }

  // Show loading during analysis
  if (isAnalyzing) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={type === 'skin' ? ['#14B8A6', '#0D9488'] : ['#EC4899', '#BE185D']}
          style={styles.loadingHeader}
        >
          <Text style={styles.loadingTitle}>Analyzing...</Text>
          <Text style={styles.loadingSubtitle}>
            Our AI is processing your {type} data
          </Text>
        </LinearGradient>
        
        <View style={styles.loadingContent}>
          <LoadingSpinner size={80} color="#14B8A6" />
          <Text style={styles.loadingText}>
            This may take a few seconds
          </Text>
          <View style={styles.loadingSteps}>
            <Text style={styles.loadingStep}>✓ Processing questionnaire data</Text>
            {capturedImage && <Text style={styles.loadingStep}>✓ Analyzing image</Text>}
            <Text style={styles.loadingStep}>⏳ Generating recommendations</Text>
          </View>
        </View>
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Analysis Failed</Text>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={performAnalysis}>
          <Text style={styles.retryText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <View style={styles.container}>
        <LinearGradient
          colors={type === 'skin' ? ['#14B8A6', '#0D9488'] : ['#EC4899', '#BE185D']}
          style={styles.header}
        >
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.title}>{type === 'skin' ? 'Skin' : 'Hair'} Analysis</Text>
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              {currentStep + 1} of {questions.length}
            </Text>
            <View style={styles.progressBar}>
              <LinearGradient
                colors={['#FFFFFF', '#FFFFFF']}
                style={[styles.progressFill, { width: `${((currentStep + 1) / questions.length) * 100}%` }]}
              />
            </View>
          </View>
        </LinearGradient>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.questionCard}>
            <FileText size={24} color="#14B8A6" style={styles.questionIcon} />
            <Text style={styles.questionText}>{currentQuestion.question}</Text>
            
            <View style={styles.optionsContainer}>
              {currentQuestion.options.map((option, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.optionButton,
                    answers[currentQuestion.id] === option && styles.optionButtonSelected
                  ]}
                  onPress={() => handleAnswerSelect(option)}
                >
                  <Text style={[
                    styles.optionText,
                    answers[currentQuestion.id] === option && styles.optionTextSelected
                  ]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <TouchableOpacity 
            style={[styles.photoCard, capturedImage && styles.photoCardSelected]}
            onPress={() => setShowImageCapture(true)}
          >
            <Camera size={24} color="#8B5CF6" />
            <Text style={styles.photoTitle}>
              {capturedImage ? 'Photo Added ✓' : 'Add Photo (Optional)'}
            </Text>
            <Text style={[styles.photoSubtitle, capturedImage && styles.photoSubtitleSelected]}>
              {capturedImage 
                ? `Quality: ${Math.round(capturedImage.quality * 100)}% - Tap to change`
                : 'Upload a photo for more accurate AI analysis'
              }
            </Text>
          </TouchableOpacity>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.nextButton, !answers[currentQuestion.id] && styles.nextButtonDisabled]}
            onPress={handleNext}
          >
            <LinearGradient
              colors={answers[currentQuestion.id] ? ['#14B8A6', '#0D9488'] : ['#D1D5DB', '#D1D5DB']}
              style={styles.nextButtonGradient}
            >
              <Text style={[
                styles.nextButtonText,
                !answers[currentQuestion.id] && styles.nextButtonTextDisabled
              ]}>
                {isLastQuestion ? (
                  'Analyze with AI'
                ) : (
                  'Next'
                )}
              </Text>
              {isLastQuestion && (
                <Sparkles size={16} color={answers[currentQuestion.id] ? '#FFFFFF' : '#9CA3AF'} />
              )}
              {!isLastQuestion && (
                <ArrowRight size={20} color={answers[currentQuestion.id] ? '#FFFFFF' : '#9CA3AF'} />
              )}
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>

      <ImageCapture
        type={type as 'skin' | 'hair'}
        visible={showImageCapture}
        onImageCaptured={handleImageCaptured}
        onCancel={() => setShowImageCapture(false)}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 24,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  backButton: {
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 16,
    textAlign: 'center',
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#E0F2FE',
    marginBottom: 8,
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  content: {
    flex: 1,
    padding: 24,
  },
  questionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  questionIcon: {
    alignSelf: 'center',
    marginBottom: 16,
  },
  questionText: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 28,
  },
  optionsContainer: {
    gap: 12,
  },
  optionButton: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  optionButtonSelected: {
    backgroundColor: '#F0FDFA',
    borderColor: '#14B8A6',
  },
  optionText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
    textAlign: 'center',
  },
  optionTextSelected: {
    color: '#14B8A6',
    fontFamily: 'Inter-Bold',
  },
  photoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderStyle: 'dashed',
  },
  photoCardSelected: {
    borderColor: '#14B8A6',
    backgroundColor: '#F0FDFA',
    borderStyle: 'solid',
  },
  photoTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginTop: 12,
    marginBottom: 4,
  },
  photoSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  photoSubtitleSelected: {
    color: '#14B8A6',
  },
  footer: {
    padding: 24,
    paddingBottom: 32,
  },
  nextButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  nextButtonDisabled: {
    opacity: 0.6,
  },
  nextButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    gap: 12,
  },
  nextButtonText: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  nextButtonTextDisabled: {
    color: '#9CA3AF',
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  loadingHeader: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 32,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  loadingTitle: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  loadingSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#E0F2FE',
    textAlign: 'center',
  },
  loadingContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginTop: 24,
    marginBottom: 32,
  },
  loadingSteps: {
    alignItems: 'flex-start',
  },
  loadingStep: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    marginBottom: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#F8FAFC',
  },
  errorTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#EF4444',
    marginBottom: 16,
  },
  errorText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#14B8A6',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  retryText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});