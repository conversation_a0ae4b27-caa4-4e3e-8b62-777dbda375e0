import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Star, ShoppingCart, Heart, Info } from 'lucide-react-native';

export default function ProductDetailsScreen() {
  const { productId } = useLocalSearchParams();
  const [isInCart, setIsInCart] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  // Mock product data - in real app, fetch based on productId
  const product = {
    id: 1,
    name: 'Gentle Argan Cleanser',
    price: 24.99,
    rating: 4.8,
    reviews: 127,
    image: 'https://images.pexels.com/photos/4465831/pexels-photo-4465831.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop',
    description: 'A gentle, nourishing cleanser infused with pure Tunisian argan oil. Perfect for all skin types, this cleanser removes impurities while maintaining your skin\'s natural moisture barrier.',
    ingredients: ['Argan Oil', 'Rose Water', 'Aloe Vera', 'Vitamin E', 'Chamomile Extract'],
    benefits: ['Deep cleansing', 'Hydrates skin', 'Anti-inflammatory', 'Suitable for sensitive skin'],
    howToUse: 'Apply to damp skin, massage gently in circular motions, then rinse with warm water. Use morning and evening.',
    skinTypes: ['All skin types', 'Sensitive skin', 'Dry skin']
  };

  const handleAddToCart = () => {
    setIsInCart(!isInCart);
  };

  const handleToggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.imageContainer}>
        <Image source={{ uri: product.image }} style={styles.productImage} />
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.favoriteButton, isFavorite && styles.favoriteButtonActive]} 
          onPress={handleToggleFavorite}
        >
          <Heart size={20} color={isFavorite ? '#FFFFFF' : '#6B7280'} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <View style={styles.productHeader}>
          <Text style={styles.productName}>{product.name}</Text>
          <Text style={styles.productPrice}>${product.price}</Text>
        </View>

        <View style={styles.ratingContainer}>
          <Star size={16} color="#F59E0B" />
          <Text style={styles.ratingText}>{product.rating}</Text>
          <Text style={styles.reviewsText}>({product.reviews} reviews)</Text>
        </View>

        <Text style={styles.description}>{product.description}</Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Benefits</Text>
          <View style={styles.benefitsContainer}>
            {product.benefits.map((benefit, index) => (
              <View key={index} style={styles.benefitTag}>
                <Text style={styles.benefitText}>{benefit}</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ingredients</Text>
          <View style={styles.ingredientsContainer}>
            {product.ingredients.map((ingredient, index) => (
              <View key={index} style={styles.ingredientItem}>
                <View style={styles.ingredientBullet} />
                <Text style={styles.ingredientText}>{ingredient}</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Info size={20} color="#14B8A6" />
            <Text style={styles.sectionTitle}>How to Use</Text>
          </View>
          <Text style={styles.howToUseText}>{product.howToUse}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Suitable For</Text>
          <View style={styles.skinTypesContainer}>
            {product.skinTypes.map((type, index) => (
              <View key={index} style={styles.skinTypeTag}>
                <Text style={styles.skinTypeText}>{type}</Text>
              </View>
            ))}
          </View>
        </View>
      </View>

      <View style={styles.footer}>
        <TouchableOpacity 
          style={[styles.addToCartButton, isInCart && styles.addToCartButtonActive]} 
          onPress={handleAddToCart}
        >
          <LinearGradient
            colors={isInCart ? ['#10B981', '#059669'] : ['#14B8A6', '#0D9488']}
            style={styles.addToCartGradient}
          >
            <ShoppingCart size={20} color="#FFFFFF" />
            <Text style={styles.addToCartText}>
              {isInCart ? 'Added to Cart' : 'Add to Cart'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  imageContainer: {
    position: 'relative',
    height: 300,
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 24,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    position: 'absolute',
    top: 60,
    right: 24,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButtonActive: {
    backgroundColor: '#EF4444',
  },
  content: {
    padding: 24,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  productName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    flex: 1,
    marginRight: 16,
  },
  productPrice: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#14B8A6',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    gap: 4,
  },
  ratingText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  reviewsText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  description: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 24,
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  benefitsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  benefitTag: {
    backgroundColor: '#F0FDFA',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#14B8A6',
  },
  benefitText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#14B8A6',
  },
  ingredientsContainer: {
    gap: 8,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  ingredientBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#14B8A6',
  },
  ingredientText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
  },
  howToUseText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 24,
  },
  skinTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  skinTypeTag: {
    backgroundColor: '#EDE9FE',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  skinTypeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#8B5CF6',
  },
  footer: {
    padding: 24,
    paddingBottom: 40,
  },
  addToCartButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  addToCartButtonActive: {
    // Additional styles for active state if needed
  },
  addToCartGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    gap: 12,
  },
  addToCartText: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
});