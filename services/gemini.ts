import { GoogleGenerativeAI } from '@google/generative-ai';
import * as FileSystem from 'expo-file-system';

export interface QuestionnaireData {
  // Skin-specific data
  skinType?: 'oily' | 'dry' | 'combination' | 'sensitive' | 'normal';
  mainConcern?: string;
  problemAreas?: string[];
  age?: number;
  lifestyle?: {
    sunExposure: 'low' | 'moderate' | 'high';
    stressLevel: 'low' | 'moderate' | 'high';
    sleepQuality: 'poor' | 'fair' | 'good' | 'excellent';
    diet: 'poor' | 'average' | 'healthy';
    exercise: 'none' | 'light' | 'moderate' | 'intense';
  };
  currentProducts?: string[];
  allergies?: string[];
  
  // Hair-specific data
  hairType?: 'straight' | 'wavy' | 'curly' | 'coily';
  scalpCondition?: 'oily' | 'dry' | 'normal' | 'sensitive' | 'flaky';
  hairConcerns?: string[];
  washingFrequency?: 'daily' | '2-3-times-week' | 'weekly' | 'less-than-weekly';
  chemicalTreatments?: boolean;
  heatStyling?: 'never' | 'rarely' | 'weekly' | 'daily';
}

export interface ImageData {
  uri: string;
  base64?: string;
  quality: number;
  metadata: {
    width: number;
    height: number;
    fileSize: number;
    lighting: 'poor' | 'fair' | 'good' | 'excellent';
    clarity: 'poor' | 'fair' | 'good' | 'excellent';
  };
}

export interface ConditionTag {
  id: string;
  category: 'skin' | 'hair';
  condition: string;
  severity: 'mild' | 'moderate' | 'severe';
  confidence: number;
  description: string;
  affectedAreas: string[];
}

export interface AIAnalysisResult {
  analysisId: string;
  timestamp: Date;
  inputType: 'questionnaire-only' | 'photo-only' | 'hybrid';
  overallConfidence: number;
  primaryCondition: string;
  conditionTags: ConditionTag[];
  personalizedInsights: string[];
  recommendedIngredients: string[];
  routineRecommendations: {
    morning: RoutineStep[];
    evening: RoutineStep[];
  };
  followUpRecommendations: string[];
}

export interface RoutineStep {
  order: number;
  productType: string;
  action: string;
  duration: string;
  frequency: string;
  importance: 'essential' | 'recommended' | 'optional';
}

class GeminiAIEngine {
  private genAI: GoogleGenerativeAI;
  private models: string[];
  private currentModelIndex: number;
  
  // Predefined condition taxonomy
  private readonly SKIN_CONDITIONS = {
    'oily-t-zone': { severity: ['mild', 'moderate', 'severe'], areas: ['forehead', 'nose', 'chin'] },
    'dry-cheeks': { severity: ['mild', 'moderate', 'severe'], areas: ['cheeks', 'jawline'] },
    'sensitive-skin': { severity: ['mild', 'moderate', 'severe'], areas: ['all'] },
    'acne-prone': { severity: ['mild', 'moderate', 'severe'], areas: ['forehead', 'nose', 'chin', 'cheeks'] },
    'hyperpigmentation': { severity: ['mild', 'moderate', 'severe'], areas: ['cheeks', 'forehead', 'chin'] },
    'aging-concerns': { severity: ['mild', 'moderate', 'severe'], areas: ['eyes', 'mouth', 'forehead'] },
    'rosacea': { severity: ['mild', 'moderate', 'severe'], areas: ['cheeks', 'nose', 'chin'] },
    'dehydrated': { severity: ['mild', 'moderate', 'severe'], areas: ['all'] }
  };

  private readonly HAIR_CONDITIONS = {
    'dandruff-grade-1': { severity: ['mild'], areas: ['scalp'] },
    'dandruff-grade-2': { severity: ['moderate'], areas: ['scalp'] },
    'dandruff-grade-3': { severity: ['severe'], areas: ['scalp'] },
    'oily-scalp': { severity: ['mild', 'moderate', 'severe'], areas: ['scalp', 'roots'] },
    'dry-ends': { severity: ['mild', 'moderate', 'severe'], areas: ['mid-length', 'ends'] },
    'hair-thinning': { severity: ['mild', 'moderate', 'severe'], areas: ['crown', 'temples', 'hairline'] },
    'color-damage': { severity: ['mild', 'moderate', 'severe'], areas: ['mid-length', 'ends'] },
    'heat-damage': { severity: ['mild', 'moderate', 'severe'], areas: ['mid-length', 'ends'] },
    'scalp-irritation': { severity: ['mild', 'moderate', 'severe'], areas: ['scalp'] }
  };

  constructor(apiKey: string) {
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.models = [
      'gemini-2.5-pro',
      'gemini-2.5-flash', 
      'gemini-2.0-flash',
      'gemini-1.5-flash'
    ];
    this.currentModelIndex = 0;
  }

  private async getModel(modelName?: string): Promise<any> {
    const model = modelName || this.models[this.currentModelIndex];
    return this.genAI.getGenerativeModel({ model });
  }

  private async generateWithFallback(prompt: string, imageData?: any): Promise<any> {
    for (let i = 0; i < this.models.length; i++) {
      try {
        const model = await this.getModel(this.models[i]);
        
        let result;
        if (imageData) {
          result = await model.generateContent([prompt, imageData]);
        } else {
          result = await model.generateContent(prompt);
        }
        
        const response = await result.response;
        return response;
      } catch (error) {
        console.warn(`Model ${this.models[i]} failed:`, error);
        
        // If this is the last model, throw the error
        if (i === this.models.length - 1) {
          throw error;
        }
        
        // Continue to next model
        continue;
      }
    }
  }

  async analyzeCondition(
    type: 'skin' | 'hair',
    questionnaire: QuestionnaireData,
    selectedAreas: string[],
    image?: ImageData
  ): Promise<AIAnalysisResult> {
    try {
      const analysisId = this.generateAnalysisId();
      const startTime = Date.now();

      // Determine analysis type
      const inputType = image ? 'hybrid' : 'questionnaire-only';
      
      // Process image if provided
      let imageAnalysis = null;
      if (image) {
        imageAnalysis = await this.analyzeImage(image, type);
      }

      // Analyze questionnaire data
      const questionnaireAnalysis = await this.analyzeQuestionnaire(questionnaire, selectedAreas, type);

      // Combine analyses using ensemble approach
      const combinedAnalysis = await this.combineAnalyses(
        questionnaireAnalysis,
        imageAnalysis,
        type,
        selectedAreas
      );

      // Generate routine recommendations
      const routineRecommendations = await this.generateRoutineRecommendations(
        combinedAnalysis.conditionTags,
        type,
        questionnaire
      );

      const responseTime = Date.now() - startTime;
      console.log(`AI Analysis completed in ${responseTime}ms`);

      return {
        analysisId,
        timestamp: new Date(),
        inputType,
        overallConfidence: combinedAnalysis.overallConfidence,
        primaryCondition: combinedAnalysis.primaryCondition,
        conditionTags: combinedAnalysis.conditionTags,
        personalizedInsights: combinedAnalysis.insights,
        recommendedIngredients: this.getRecommendedIngredients(combinedAnalysis.conditionTags),
        routineRecommendations,
        followUpRecommendations: this.generateFollowUpRecommendations(combinedAnalysis.conditionTags)
      };

    } catch (error) {
      console.error('AI Analysis Error:', error);
      throw new Error(`Analysis failed: ${error.message}`);
    }
  }

  private async analyzeImage(image: ImageData, type: 'skin' | 'hair'): Promise<any> {
    try {
      // Validate image quality
      if (image.quality < 0.6) {
        throw new Error('Image quality too low for accurate analysis');
      }

      // Prepare image for Gemini Vision
      const imageBase64 = await this.preprocessImage(image);
      
      const prompt = this.buildImageAnalysisPrompt(type, image.metadata);
      
      const imageData = {
        inlineData: {
          data: imageBase64,
          mimeType: 'image/jpeg'
        }
      };
      
      const response = await this.generateWithFallback(prompt, imageData);
      const analysisText = response.text();
      
      return this.parseImageAnalysis(analysisText, type);
    } catch (error) {
      console.error('Image analysis error:', error);
      return null; // Fallback to questionnaire-only analysis
    }
  }

  private async analyzeQuestionnaire(
    questionnaire: QuestionnaireData,
    selectedAreas: string[],
    type: 'skin' | 'hair'
  ): Promise<any> {
    const prompt = this.buildQuestionnairePrompt(questionnaire, selectedAreas, type);
    
    const response = await this.generateWithFallback(prompt);
    const analysisText = response.text();
    
    return this.parseQuestionnaireAnalysis(analysisText, type);
  }

  private async combineAnalyses(
    questionnaireAnalysis: any,
    imageAnalysis: any,
    type: 'skin' | 'hair',
    selectedAreas: string[]
  ): Promise<any> {
    // Weight calculations based on data quality
    const questionnaireWeight = 0.7;
    const imageWeight = imageAnalysis ? 0.3 : 0;
    
    // Combine condition tags with confidence weighting
    const combinedTags = this.mergeConditionTags(
      questionnaireAnalysis.tags,
      imageAnalysis?.tags || [],
      questionnaireWeight,
      imageWeight
    );

    // Calculate overall confidence
    const overallConfidence = this.calculateOverallConfidence(combinedTags, imageAnalysis);

    // Determine primary condition
    const primaryCondition = combinedTags.reduce((prev, current) => 
      prev.confidence > current.confidence ? prev : current
    ).condition;

    // Generate personalized insights
    const insights = await this.generatePersonalizedInsights(combinedTags, selectedAreas, type);

    return {
      conditionTags: combinedTags,
      overallConfidence,
      primaryCondition,
      insights
    };
  }

  private buildImageAnalysisPrompt(type: 'skin' | 'hair', metadata: any): string {
    const conditions = type === 'skin' ? Object.keys(this.SKIN_CONDITIONS) : Object.keys(this.HAIR_CONDITIONS);
    
    return `
You are an expert dermatologist AI analyzing a ${type} image. 

Image Quality Metrics:
- Resolution: ${metadata.width}x${metadata.height}
- Lighting: ${metadata.lighting}
- Clarity: ${metadata.clarity}

Please analyze this ${type} image and identify visible conditions from this taxonomy:
${conditions.join(', ')}

For each identified condition, provide:
1. Condition name (from taxonomy)
2. Severity (mild/moderate/severe)
3. Confidence score (0-100)
4. Visible indicators that led to this assessment
5. Affected areas

Focus on Tunisian climate considerations and natural ingredient compatibility.

Respond in JSON format:
{
  "conditions": [
    {
      "condition": "condition-name",
      "severity": "mild|moderate|severe",
      "confidence": 85,
      "indicators": ["visible sign 1", "visible sign 2"],
      "areas": ["area1", "area2"]
    }
  ],
  "imageQualityScore": 85,
  "analysisNotes": "Additional observations"
}
`;
  }

  private buildQuestionnairePrompt(
    questionnaire: QuestionnaireData,
    selectedAreas: string[],
    type: 'skin' | 'hair'
  ): string {
    const conditions = type === 'skin' ? Object.keys(this.SKIN_CONDITIONS) : Object.keys(this.HAIR_CONDITIONS);
    
    return `
You are an expert dermatologist AI analyzing ${type} concerns based on user questionnaire data.

User Data:
${JSON.stringify(questionnaire, null, 2)}

Selected Problem Areas: ${selectedAreas.join(', ')}

Analysis Requirements:
1. Identify likely conditions from this taxonomy: ${conditions.join(', ')}
2. Consider Tunisian climate factors (hot, dry summers; mild winters)
3. Prioritize natural, bio-cosmetic solutions
4. Account for lifestyle factors and current routine

For each identified condition, provide:
- Condition name (from taxonomy)
- Severity assessment
- Confidence score (0-100)
- Reasoning based on questionnaire responses
- Affected areas correlation

Respond in JSON format:
{
  "conditions": [
    {
      "condition": "condition-name",
      "severity": "mild|moderate|severe", 
      "confidence": 85,
      "reasoning": "Based on user responses...",
      "areas": ["area1", "area2"]
    }
  ],
  "riskFactors": ["factor1", "factor2"],
  "lifestyleImpact": "assessment of lifestyle factors"
}
`;
  }

  private mergeConditionTags(
    questionnaireeTags: any[],
    imageTags: any[],
    qWeight: number,
    iWeight: number
  ): ConditionTag[] {
    const tagMap = new Map<string, ConditionTag>();

    // Process questionnaire tags
    questionnaireeTags.forEach(tag => {
      const weightedConfidence = tag.confidence * qWeight;
      tagMap.set(tag.condition, {
        id: this.generateTagId(),
        category: tag.category || 'skin',
        condition: tag.condition,
        severity: tag.severity,
        confidence: Math.round(weightedConfidence),
        description: tag.reasoning || tag.description,
        affectedAreas: tag.areas || []
      });
    });

    // Process and merge image tags
    imageTags.forEach(tag => {
      const existing = tagMap.get(tag.condition);
      if (existing) {
        // Merge with existing tag
        const combinedConfidence = existing.confidence + (tag.confidence * iWeight);
        existing.confidence = Math.min(100, Math.round(combinedConfidence));
        existing.description += ` Visual analysis confirms: ${tag.indicators?.join(', ') || 'visible signs detected'}.`;
      } else {
        // Add new tag from image analysis
        tagMap.set(tag.condition, {
          id: this.generateTagId(),
          category: tag.category || 'skin',
          condition: tag.condition,
          severity: tag.severity,
          confidence: Math.round(tag.confidence * iWeight),
          description: `Visual analysis indicates: ${tag.indicators?.join(', ') || 'condition detected'}.`,
          affectedAreas: tag.areas || []
        });
      }
    });

    return Array.from(tagMap.values())
      .filter(tag => tag.confidence >= 30) // Filter low-confidence tags
      .sort((a, b) => b.confidence - a.confidence); // Sort by confidence
  }

  private calculateOverallConfidence(tags: ConditionTag[], imageAnalysis: any): number {
    if (tags.length === 0) return 0;
    
    const avgConfidence = tags.reduce((sum, tag) => sum + tag.confidence, 0) / tags.length;
    
    // Boost confidence if image analysis is available and high quality
    const imageBoost = imageAnalysis?.imageQualityScore > 80 ? 10 : 0;
    
    return Math.min(100, Math.round(avgConfidence + imageBoost));
  }

  private async generatePersonalizedInsights(
    tags: ConditionTag[],
    selectedAreas: string[],
    type: 'skin' | 'hair'
  ): Promise<string[]> {
    const prompt = `
Based on the following ${type} analysis results, generate 3-5 personalized insights for a user in Tunisia:

Identified Conditions:
${tags.map(tag => `- ${tag.condition} (${tag.severity}, ${tag.confidence}% confidence)`).join('\n')}

Selected Problem Areas: ${selectedAreas.join(', ')}

Generate insights that:
1. Explain the connection between conditions and lifestyle
2. Provide climate-specific advice for Tunisia
3. Suggest natural ingredient benefits
4. Offer realistic expectations and timeline
5. Include preventive measures

Format as a JSON array of insight strings.
`;

    try {
      const response = await this.generateWithFallback(prompt);
      const insightsText = response.text();
      
      // Parse JSON response
      const jsonMatch = insightsText.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.error('Insights generation error:', error);
    }

    // Fallback insights
    return this.getFallbackInsights(tags, type);
  }

  private async generateRoutineRecommendations(
    tags: ConditionTag[],
    type: 'skin' | 'hair',
    questionnaire: QuestionnaireData
  ): Promise<{ morning: RoutineStep[]; evening: RoutineStep[] }> {
    const prompt = `
Create a personalized ${type} routine for someone with these conditions:
${tags.map(tag => `- ${tag.condition} (${tag.severity})`).join('\n')}

User Profile:
- Age: ${questionnaire.age || 'not specified'}
- Lifestyle: ${JSON.stringify(questionnaire.lifestyle || {})}
- Current routine experience: ${questionnaire.routineExperience || 'beginner'}

Requirements:
1. Use Tunisian bio-cosmetic ingredients (argan oil, rose water, olive oil, prickly pear, etc.)
2. Consider hot, dry climate
3. Create morning and evening routines
4. Include step order, duration, frequency, and importance level
5. Keep routines simple and achievable

Respond in JSON format:
{
  "morning": [
    {
      "order": 1,
      "productType": "cleanser",
      "action": "Gentle cleansing with argan oil cleanser",
      "duration": "2 minutes",
      "frequency": "daily",
      "importance": "essential"
    }
  ],
  "evening": [...]
}
`;

    try {
      const response = await this.generateWithFallback(prompt);
      const routineText = response.text();
      
      const jsonMatch = routineText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.error('Routine generation error:', error);
    }

    // Fallback routine
    return this.getFallbackRoutine(type, tags);
  }

  private async preprocessImage(image: ImageData): Promise<string> {
    try {
      // Read image file
      const imageInfo = await FileSystem.getInfoAsync(image.uri);
      if (!imageInfo.exists) {
        throw new Error('Image file not found');
      }

      // Validate file size (max 10MB)
      if (imageInfo.size && imageInfo.size > 10 * 1024 * 1024) {
        throw new Error('Image file too large (max 10MB)');
      }

      // Read as base64
      const base64 = await FileSystem.readAsStringAsync(image.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      return base64;
    } catch (error) {
      console.error('Image preprocessing error:', error);
      throw error;
    }
  }

  private parseImageAnalysis(analysisText: string, type: 'skin' | 'hair'): any {
    try {
      const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          tags: parsed.conditions?.map(condition => ({
            ...condition,
            category: type
          })) || [],
          imageQualityScore: parsed.imageQualityScore || 70,
          notes: parsed.analysisNotes || ''
        };
      }
    } catch (error) {
      console.error('Failed to parse image analysis:', error);
    }
    
    return { tags: [], imageQualityScore: 50, notes: 'Analysis parsing failed' };
  }

  private parseQuestionnaireAnalysis(analysisText: string, type: 'skin' | 'hair'): any {
    try {
      const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          tags: parsed.conditions?.map(condition => ({
            ...condition,
            category: type
          })) || [],
          riskFactors: parsed.riskFactors || [],
          lifestyleImpact: parsed.lifestyleImpact || ''
        };
      }
    } catch (error) {
      console.error('Failed to parse questionnaire analysis:', error);
    }
    
    return { tags: [], riskFactors: [], lifestyleImpact: '' };
  }

  private getRecommendedIngredients(tags: ConditionTag[]): string[] {
    const ingredientMap = {
      'oily-t-zone': ['tea tree oil', 'salicylic acid', 'niacinamide'],
      'dry-cheeks': ['argan oil', 'hyaluronic acid', 'ceramides'],
      'sensitive-skin': ['aloe vera', 'chamomile', 'rose water'],
      'acne-prone': ['tea tree oil', 'zinc', 'bentonite clay'],
      'hyperpigmentation': ['vitamin C', 'arbutin', 'kojic acid'],
      'aging-concerns': ['retinol', 'peptides', 'antioxidants'],
      'dandruff-grade-1': ['tea tree oil', 'zinc pyrithione'],
      'dandruff-grade-2': ['ketoconazole', 'selenium sulfide'],
      'dandruff-grade-3': ['coal tar', 'salicylic acid'],
      'oily-scalp': ['tea tree oil', 'peppermint oil'],
      'dry-ends': ['argan oil', 'shea butter', 'protein'],
      'hair-thinning': ['rosemary oil', 'caffeine', 'biotin']
    };

    const ingredients = new Set<string>();
    tags.forEach(tag => {
      const tagIngredients = ingredientMap[tag.condition] || [];
      tagIngredients.forEach(ingredient => ingredients.add(ingredient));
    });

    return Array.from(ingredients);
  }

  private getFallbackInsights(tags: ConditionTag[], type: 'skin' | 'hair'): string[] {
    const baseInsights = type === 'skin' ? [
      'Your skin shows signs that are common in Tunisia\'s climate - focus on hydration and sun protection.',
      'Natural ingredients like argan oil and rose water are excellent for your skin type.',
      'Consistency is key - follow your routine for at least 4-6 weeks to see results.'
    ] : [
      'Hair health in Tunisia requires extra moisture due to the dry climate.',
      'Natural oils like argan and olive oil can significantly improve hair condition.',
      'Protect your hair from sun damage with natural UV-blocking ingredients.'
    ];

    return baseInsights;
  }

  private getFallbackRoutine(type: 'skin' | 'hair', tags: ConditionTag[]): { morning: RoutineStep[]; evening: RoutineStep[] } {
    if (type === 'skin') {
      return {
        morning: [
          {
            order: 1,
            productType: 'cleanser',
            action: 'Gentle cleansing with argan oil cleanser',
            duration: '2 minutes',
            frequency: 'daily',
            importance: 'essential'
          },
          {
            order: 2,
            productType: 'moisturizer',
            action: 'Apply lightweight moisturizer with SPF',
            duration: '1 minute',
            frequency: 'daily',
            importance: 'essential'
          }
        ],
        evening: [
          {
            order: 1,
            productType: 'cleanser',
            action: 'Deep cleansing with argan oil cleanser',
            duration: '2 minutes',
            frequency: 'daily',
            importance: 'essential'
          },
          {
            order: 2,
            productType: 'toner',
            action: 'Apply rose water toner',
            duration: '30 seconds',
            frequency: 'daily',
            importance: 'recommended'
          },
          {
            order: 3,
            productType: 'moisturizer',
            action: 'Apply nourishing night moisturizer',
            duration: '1 minute',
            frequency: 'daily',
            importance: 'essential'
          }
        ]
      };
    } else {
      return {
        morning: [
          {
            order: 1,
            productType: 'leave-in',
            action: 'Apply argan oil leave-in treatment',
            duration: '1 minute',
            frequency: 'daily',
            importance: 'recommended'
          }
        ],
        evening: [
          {
            order: 1,
            productType: 'oil-treatment',
            action: 'Scalp massage with rosemary oil blend',
            duration: '5 minutes',
            frequency: '3x per week',
            importance: 'essential'
          }
        ]
      };
    }
  }

  private generateFollowUpRecommendations(tags: ConditionTag[]): string[] {
    return [
      'Take progress photos weekly in the same lighting conditions',
      'Track your routine consistency in the app',
      'Schedule a follow-up analysis in 4 weeks',
      'Consider consulting a dermatologist if conditions worsen'
    ];
  }

  private generateAnalysisId(): string {
    return `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateTagId(): string {
    return `tag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Performance monitoring
  async getAnalyticsData(): Promise<any> {
    return {
      totalAnalyses: 0, // Would be tracked in real implementation
      averageResponseTime: 0,
      accuracyRate: 0,
      userSatisfactionScore: 0,
      modelUsageStats: this.models.map((model, index) => ({
        model,
        successRate: 0, // Would be tracked in real implementation
        averageResponseTime: 0
      }))
    };
  }
}

// Export singleton instance
export const geminiAI = new GeminiAIEngine(
  process.env.EXPO_PUBLIC_GEMINI_API_KEY || 'demo-key'
);

// Export types for use in other modules
export type {
  QuestionnaireData,
  ImageData,
  ConditionTag,
  AIAnalysisResult,
  RoutineStep
};