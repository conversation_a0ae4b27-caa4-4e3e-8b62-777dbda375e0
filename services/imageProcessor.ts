import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { ImageData } from './gemini';

export interface ImageProcessingOptions {
  maxWidth: number;
  maxHeight: number;
  quality: number;
  allowsEditing: boolean;
}

export class ImageProcessor {
  private static readonly DEFAULT_OPTIONS: ImageProcessingOptions = {
    maxWidth: 1024,
    maxHeight: 1024,
    quality: 0.8,
    allowsEditing: true
  };

  static async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Permission request failed:', error);
      return false;
    }
  }

  static async captureImage(options?: Partial<ImageProcessingOptions>): Promise<ImageData | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        throw new Error('Camera permission not granted');
      }

      const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
      
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: finalOptions.allowsEditing,
        aspect: [1, 1],
        quality: finalOptions.quality,
        base64: false, // We'll read it separately for better memory management
      });

      if (result.canceled || !result.assets[0]) {
        return null;
      }

      const asset = result.assets[0];
      
      // Validate image
      await this.validateImage(asset.uri);
      
      // Process and analyze image quality
      const processedImage = await this.processImage(asset, finalOptions);
      
      return processedImage;
    } catch (error) {
      console.error('Image capture failed:', error);
      throw error;
    }
  }

  static async takePhoto(options?: Partial<ImageProcessingOptions>): Promise<ImageData | null> {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Camera permission not granted');
      }

      const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
      
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: finalOptions.allowsEditing,
        aspect: [1, 1],
        quality: finalOptions.quality,
        base64: false,
      });

      if (result.canceled || !result.assets[0]) {
        return null;
      }

      const asset = result.assets[0];
      
      // Validate and process
      await this.validateImage(asset.uri);
      const processedImage = await this.processImage(asset, finalOptions);
      
      return processedImage;
    } catch (error) {
      console.error('Photo capture failed:', error);
      throw error;
    }
  }

  private static async validateImage(uri: string): Promise<void> {
    const info = await FileSystem.getInfoAsync(uri);
    
    if (!info.exists) {
      throw new Error('Image file does not exist');
    }

    if (info.size && info.size > 10 * 1024 * 1024) { // 10MB limit
      throw new Error('Image file too large (max 10MB)');
    }
  }

  private static async processImage(
    asset: ImagePicker.ImagePickerAsset,
    options: ImageProcessingOptions
  ): Promise<ImageData> {
    try {
      // Analyze image quality
      const qualityMetrics = await this.analyzeImageQuality(asset);
      
      // Calculate overall quality score
      const qualityScore = this.calculateQualityScore(qualityMetrics);
      
      return {
        uri: asset.uri,
        quality: qualityScore,
        metadata: {
          width: asset.width,
          height: asset.height,
          fileSize: asset.fileSize || 0,
          lighting: qualityMetrics.lighting,
          clarity: qualityMetrics.clarity
        }
      };
    } catch (error) {
      console.error('Image processing failed:', error);
      throw error;
    }
  }

  private static async analyzeImageQuality(asset: ImagePicker.ImagePickerAsset): Promise<{
    lighting: 'poor' | 'fair' | 'good' | 'excellent';
    clarity: 'poor' | 'fair' | 'good' | 'excellent';
    composition: 'poor' | 'fair' | 'good' | 'excellent';
  }> {
    // Basic quality assessment based on available metadata
    // In a real implementation, this would use computer vision algorithms
    
    const { width, height, fileSize } = asset;
    
    // Assess resolution
    const resolution = width * height;
    let clarity: 'poor' | 'fair' | 'good' | 'excellent';
    
    if (resolution < 300000) clarity = 'poor';
    else if (resolution < 800000) clarity = 'fair';
    else if (resolution < 2000000) clarity = 'good';
    else clarity = 'excellent';

    // Assess file size relative to resolution (indicates compression/quality)
    const bytesPerPixel = fileSize ? fileSize / resolution : 1;
    let lighting: 'poor' | 'fair' | 'good' | 'excellent';
    
    if (bytesPerPixel < 0.5) lighting = 'poor';
    else if (bytesPerPixel < 1) lighting = 'fair';
    else if (bytesPerPixel < 2) lighting = 'good';
    else lighting = 'excellent';

    // Basic composition assessment (aspect ratio)
    const aspectRatio = width / height;
    const composition = (aspectRatio >= 0.8 && aspectRatio <= 1.2) ? 'good' : 'fair';

    return { lighting, clarity, composition };
  }

  private static calculateQualityScore(metrics: {
    lighting: string;
    clarity: string;
    composition: string;
  }): number {
    const scoreMap = { poor: 0.2, fair: 0.5, good: 0.8, excellent: 1.0 };
    
    const lightingScore = scoreMap[metrics.lighting] || 0.5;
    const clarityScore = scoreMap[metrics.clarity] || 0.5;
    const compositionScore = scoreMap[metrics.composition] || 0.5;
    
    // Weighted average (clarity is most important)
    const weightedScore = (clarityScore * 0.5) + (lightingScore * 0.3) + (compositionScore * 0.2);
    
    return Math.round(weightedScore * 100) / 100;
  }

  static async optimizeForAnalysis(imageUri: string): Promise<string> {
    try {
      // In a real implementation, this would apply:
      // - Noise reduction
      // - Lighting normalization
      // - Contrast enhancement
      // - Face/scalp detection and cropping
      
      // For now, return the original URI
      // The actual optimization would be done using image processing libraries
      return imageUri;
    } catch (error) {
      console.error('Image optimization failed:', error);
      return imageUri; // Return original on failure
    }
  }

  static getImageGuidelines(type: 'skin' | 'hair'): string[] {
    if (type === 'skin') {
      return [
        'Take photo in natural daylight or bright, even lighting',
        'Clean your face and remove makeup before taking the photo',
        'Hold camera 12-18 inches away from your face',
        'Look directly at camera with neutral expression',
        'Avoid shadows and harsh lighting',
        'Ensure the entire face is visible and in focus'
      ];
    } else {
      return [
        'Take photo in bright, natural lighting',
        'Part your hair to show scalp clearly',
        'Take multiple angles if needed (top, sides)',
        'Ensure hair is clean and styled naturally',
        'Avoid flash photography',
        'Show problem areas clearly without obstruction'
      ];
    }
  }
}

export default ImageProcessor;