export interface AnalyticsEvent {
  eventName: string;
  userId?: string;
  sessionId: string;
  timestamp: Date;
  properties: Record<string, any>;
}

export interface AIPerformanceMetrics {
  analysisId: string;
  responseTime: number;
  confidence: number;
  accuracy?: number;
  userFeedback?: number;
  conditionsDetected: number;
}

export interface UserEngagementMetrics {
  userId: string;
  sessionDuration: number;
  screensVisited: string[];
  actionsCompleted: string[];
  conversionEvents: string[];
}

class AnalyticsService {
  private events: AnalyticsEvent[] = [];
  private performanceMetrics: AIPerformanceMetrics[] = [];
  private sessionId: string;

  constructor() {
    this.sessionId = this.generateSessionId();
  }

  // Track user events
  trackEvent(eventName: string, properties: Record<string, any> = {}, userId?: string): void {
    const event: AnalyticsEvent = {
      eventName,
      userId,
      sessionId: this.sessionId,
      timestamp: new Date(),
      properties
    };

    this.events.push(event);
    this.sendToAnalytics(event);
  }

  // Track AI performance
  trackAIPerformance(metrics: AIPerformanceMetrics): void {
    this.performanceMetrics.push(metrics);
    
    // Track as event for dashboard
    this.trackEvent('ai_analysis_completed', {
      analysisId: metrics.analysisId,
      responseTime: metrics.responseTime,
      confidence: metrics.confidence,
      conditionsDetected: metrics.conditionsDetected
    });
  }

  // Track user journey milestones
  trackUserJourney(milestone: string, additionalData: Record<string, any> = {}): void {
    this.trackEvent('user_journey_milestone', {
      milestone,
      ...additionalData
    });
  }

  // Track conversion events
  trackConversion(conversionType: string, value?: number): void {
    this.trackEvent('conversion', {
      type: conversionType,
      value,
      timestamp: new Date().toISOString()
    });
  }

  // Track user feedback
  trackUserFeedback(analysisId: string, rating: number, feedback?: string): void {
    this.trackEvent('user_feedback', {
      analysisId,
      rating,
      feedback,
      category: 'ai_analysis'
    });

    // Update performance metrics
    const metric = this.performanceMetrics.find(m => m.analysisId === analysisId);
    if (metric) {
      metric.userFeedback = rating;
    }
  }

  // Get performance insights
  getPerformanceInsights(): {
    averageResponseTime: number;
    averageConfidence: number;
    averageUserRating: number;
    totalAnalyses: number;
  } {
    if (this.performanceMetrics.length === 0) {
      return {
        averageResponseTime: 0,
        averageConfidence: 0,
        averageUserRating: 0,
        totalAnalyses: 0
      };
    }

    const totalResponseTime = this.performanceMetrics.reduce((sum, m) => sum + m.responseTime, 0);
    const totalConfidence = this.performanceMetrics.reduce((sum, m) => sum + m.confidence, 0);
    const ratingsWithFeedback = this.performanceMetrics.filter(m => m.userFeedback);
    const totalRating = ratingsWithFeedback.reduce((sum, m) => sum + (m.userFeedback || 0), 0);

    return {
      averageResponseTime: totalResponseTime / this.performanceMetrics.length,
      averageConfidence: totalConfidence / this.performanceMetrics.length,
      averageUserRating: ratingsWithFeedback.length > 0 ? totalRating / ratingsWithFeedback.length : 0,
      totalAnalyses: this.performanceMetrics.length
    };
  }

  // A/B Testing support
  getExperimentVariant(experimentName: string, userId: string): 'A' | 'B' {
    // Simple hash-based assignment for consistent user experience
    const hash = this.simpleHash(userId + experimentName);
    return hash % 2 === 0 ? 'A' : 'B';
  }

  trackExperimentExposure(experimentName: string, variant: 'A' | 'B', userId: string): void {
    this.trackEvent('experiment_exposure', {
      experimentName,
      variant,
      userId
    });
  }

  private sendToAnalytics(event: AnalyticsEvent): void {
    // In production, this would send to your analytics service
    // For now, we'll just log to console
    console.log('Analytics Event:', event);
    
    // Example integrations:
    // - Firebase Analytics
    // - Mixpanel
    // - Amplitude
    // - Custom analytics endpoint
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  // GDPR Compliance methods
  anonymizeUserData(userId: string): void {
    // Remove or anonymize user-specific data
    this.events = this.events.map(event => ({
      ...event,
      userId: event.userId === userId ? 'anonymized' : event.userId
    }));
  }

  exportUserData(userId: string): AnalyticsEvent[] {
    return this.events.filter(event => event.userId === userId);
  }

  deleteUserData(userId: string): void {
    this.events = this.events.filter(event => event.userId !== userId);
    this.performanceMetrics = this.performanceMetrics.filter(metric => 
      !metric.analysisId.includes(userId)
    );
  }
}

export const analytics = new AnalyticsService();
export default AnalyticsService;